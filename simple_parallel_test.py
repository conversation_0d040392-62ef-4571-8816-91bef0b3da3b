#!/usr/bin/env python3
"""
Simple test to verify the parallel routing logic with real LangGraph Send API.
"""

import json
from typing import List, Union

# Use the real Send class from LangGraph
try:
    from langgraph.types import Send
    print("✅ Using real LangGraph Send API")
except ImportError:
    # Fallback to mock if not available
    class Send:
        def __init__(self, node: str, state: dict):
            self.node = node
            self.state = state

        def __repr__(self):
            return f"Send(node='{self.node}', state_keys={list(self.state.keys())})"
    print("⚠️ Using mock Send class")

class AIMessage:
    def __init__(self, content: str):
        self.content = content

class OrchestratorRoutes:
    REPOSITORY_EXPLORER = "repository_explorer_agent"
    CODE_NAVIGATOR = "code_navigator_agent"
    GITLAB_ECOSYSTEM = "gitlab_ecosystem_agent"
    GIT_HISTORY = "git_history_agent"
    CONTEXT_SYNTHESIZER = "context_synthesizer_agent"
    STOP = "plan_terminator"

def mock_router_logic(state: dict) -> Union[str, List[Send]]:
    """
    Simplified version of the orchestrator router logic.
    """
    # Get last message from orchestrator
    conversation_history = state.get("conversation_history", {})
    if "context_2_0_orchestrator" not in conversation_history:
        return OrchestratorRoutes.STOP

    orchestrator_messages = conversation_history["context_2_0_orchestrator"]
    if not orchestrator_messages:
        return OrchestratorRoutes.STOP

    last_message = orchestrator_messages[-1]

    # Parse orchestrator's JSON routing decision
    if isinstance(last_message, AIMessage):
        try:
            # Extract JSON from message content
            content = last_message.content.strip()
            
            # Remove markdown code block markers if present
            if content.startswith("```json"):
                content = content[7:]
            if content.startswith("```"):
                content = content[3:]
            if content.endswith("```"):
                content = content[:-3]
            
            content = content.strip()
            
            # Parse JSON
            routing_data = json.loads(content)
            
            # Extract routing decision
            if "routing_decision" in routing_data:
                decision = routing_data["routing_decision"]
                
                # Map agent IDs to routes
                agent_id_to_route = {
                    "repository_explorer": OrchestratorRoutes.REPOSITORY_EXPLORER,
                    "code_navigator": OrchestratorRoutes.CODE_NAVIGATOR,
                    "gitlab_ecosystem": OrchestratorRoutes.GITLAB_ECOSYSTEM,
                    "git_history": OrchestratorRoutes.GIT_HISTORY,
                    "context_synthesizer": OrchestratorRoutes.CONTEXT_SYNTHESIZER,
                }
                
                # Handle single agent routing
                if "next_agent" in decision:
                    next_agent = decision["next_agent"].lower()
                    if next_agent in agent_id_to_route:
                        print(f"🎯 Single agent routing: {next_agent}")
                        return agent_id_to_route[next_agent]
                
                # Handle parallel agent routing (TRUE PARALLEL EXECUTION)
                elif "next_agents" in decision:
                    next_agents = decision["next_agents"]
                    if next_agents and len(next_agents) > 0:
                        # Convert to lowercase for consistent matching
                        agent_ids = [agent.lower() for agent in next_agents]
                        
                        # Create Send objects for parallel execution
                        parallel_sends = []
                        for agent_id in agent_ids:
                            if agent_id in agent_id_to_route:
                                agent_node = agent_id_to_route[agent_id]
                                # Create Send object with focus areas for this agent
                                focus_areas = decision.get("focus_areas", {}).get(agent_id, [])
                                agent_state = state.copy()
                                agent_state["current_agent"] = agent_id
                                agent_state["agent_focus_areas"] = focus_areas
                                
                                parallel_sends.append(Send(agent_node, agent_state))
                        
                        if parallel_sends:
                            print(f"🚀 PARALLEL EXECUTION: {next_agents} ({len(parallel_sends)} agents)")
                            return parallel_sends
                        else:
                            print("❌ No valid agents for parallel execution")
                            return OrchestratorRoutes.STOP
        
        except json.JSONDecodeError as e:
            print(f"❌ Failed to parse JSON: {e}")
            return OrchestratorRoutes.STOP
        except Exception as e:
            print(f"❌ Error processing routing decision: {e}")
            return OrchestratorRoutes.STOP
    
    return OrchestratorRoutes.STOP


def test_parallel_routing():
    """Test parallel routing logic."""
    print("🧪 Testing parallel routing...")
    
    # Create parallel routing decision
    parallel_decision = {
        "routing_decision": {
            "next_agents": ["repository_explorer", "code_navigator"],
            "reasoning": "Need to simultaneously examine project structure and analyze code",
            "focus_areas": {
                "repository_explorer": ["project_structure", "config"],
                "code_navigator": ["implementation_details", "code_patterns"]
            },
            "investigation_type": "parallel"
        }
    }
    
    # Create test state
    state = {
        "conversation_history": {
            "context_2_0_orchestrator": [
                AIMessage(content=json.dumps(parallel_decision, indent=2))
            ]
        },
        "status": "IN_PROGRESS"
    }
    
    # Test the router
    result = mock_router_logic(state)
    
    print(f"🔍 Result type: {type(result)}")
    print(f"🔍 Result: {result}")
    
    if isinstance(result, list) and all(isinstance(item, Send) for item in result):
        print("✅ SUCCESS: Parallel routing works!")
        for i, send_obj in enumerate(result):
            print(f"  Agent {i+1}: {send_obj}")
        return True
    else:
        print("❌ FAILURE: Expected list of Send objects")
        return False


def test_single_routing():
    """Test single agent routing logic."""
    print("\n🧪 Testing single agent routing...")
    
    # Create single routing decision
    single_decision = {
        "routing_decision": {
            "next_agent": "repository_explorer",
            "reasoning": "Need to analyze project structure first",
            "focus_areas": ["project_layout", "dependencies"]
        }
    }
    
    # Create test state
    state = {
        "conversation_history": {
            "context_2_0_orchestrator": [
                AIMessage(content=json.dumps(single_decision, indent=2))
            ]
        },
        "status": "IN_PROGRESS"
    }
    
    # Test the router
    result = mock_router_logic(state)
    
    print(f"🔍 Result: {result}")
    
    if result == OrchestratorRoutes.REPOSITORY_EXPLORER:
        print("✅ SUCCESS: Single agent routing works!")
        return True
    else:
        print("❌ FAILURE: Expected repository_explorer_agent")
        return False


def main():
    """Run tests."""
    print("🚀 Testing True Parallel Routing Logic")
    print("=" * 50)
    
    parallel_success = test_parallel_routing()
    single_success = test_single_routing()
    
    print("\n" + "=" * 50)
    print("📊 SUMMARY")
    print(f"Parallel routing: {'✅ PASS' if parallel_success else '❌ FAIL'}")
    print(f"Single routing: {'✅ PASS' if single_success else '❌ FAIL'}")
    
    if parallel_success and single_success:
        print("\n🎉 All tests passed! The logic works correctly.")
    else:
        print("\n❌ Some tests failed.")


if __name__ == "__main__":
    main()

1. Understand the prompts for each agent and how they are implemented - are they forcing a certain structure? Are they promoting more tool use than necessary?
2. Why is the orchestrator not ingesting and everything and moving forward? What happens at the last context_2_0_orchestrator that it never goes beyond?


3. In the @context_2_0 gitlab-ai-gateway/********************/agents/context_2_0 folder The agent as a tool is working yay! But we need to do better here -> What is happening is ex: repository_code_navigator_agent decided to call call_gitlab_ecosystem  -> gitlab_ecosystem_agent agent, the gitlab ecosystem agent comes in and ends up calling back call_repository_code_navigator - meaning it thought that it's best to call repo code navigator agent as it might be better able to answer the question while we actually wanted to use the core tools of the gitlab ecosystem. 
This can be resolved by only exposing the core functionality tools during such agent as tool calls, and not exposing the agent as a tool in the called agent so - this is inherently supporting our intention of only going into one depth of the agent as a tool by explictly not providing the agent as tools option in the called agent as tool. Ins impler words, in this flow, the gitlab_ecosystem_agent should not be sent the agent as a tool option, meaning the tool registry should not provide tools like call_repository_code_navigator in this flow.


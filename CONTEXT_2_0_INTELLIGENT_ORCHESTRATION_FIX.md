# Context 2.0 Intelligent Orchestration Fix

## Problem

The orchestrator was too rigid and deterministic, following hardcoded sequences instead of making intelligent, adaptive decisions based on the query and findings.

### Issues with Previous Approach

1. **Overly Deterministic**: Hardcoded sequence (Repository Explorer → Code Navigator → GitLab Ecosystem → Git History → Synthesizer)
2. **No Parallel Execution**: Could only route to one agent at a time
3. **No Adaptive Depth**: Couldn't dig deeper based on findings
4. **Query-Agnostic**: Same sequence regardless of query type
5. **No Iterative Investigation**: Couldn't re-invoke agents with refined focus

### Example of Rigid Behavior

**Previous Prompt**:
```
**Selection Guidelines:**
- For `code_analysis`: Repository Explorer + Code Navigator + Context Synthesizer
- For `bug_investigation`: Code Navigator + GitLab Ecosystem + Git History + Context Synthesizer

**Sequential Routing**: Route to specialists in priority order:
- Start with Repository Explorer for structure
- Then Code Navigator for implementation
- Then GitLab Ecosystem for project context
```

This made the system follow predetermined paths regardless of what the query actually needed.

## Solution: Intelligent Adaptive Orchestration

### Key Changes

#### 1. Query-Driven Agent Selection

**Before**: Hardcoded sequences based on goal type
**After**: Strategic selection based on what the query actually needs

```
**Examples of Intelligent Selection:**
- "Fix authentication bug" → Code Navigator (find auth code) + GitLab Ecosystem (recent auth issues) in parallel
- "How does X feature work?" → Repository Explorer (find feature) → Code Navigator (analyze implementation)
- "Plan Y feature" → GitLab Ecosystem (existing discussions) + Repository Explorer (architecture) in parallel
- "Performance issue in Z" → Code Navigator (analyze Z) → Git History (recent Z changes) → Repository Explorer (config)
```

#### 2. Parallel Agent Routing

**New JSON Schema** supports parallel routing:

```json
{
  "routing_decision": {
    "next_agents": ["repository_explorer", "gitlab_ecosystem"],
    "reasoning": "Can simultaneously analyze project structure and check for related issues/discussions.",
    "focus_areas": {
      "repository_explorer": ["auth_config", "dependencies", "architecture"],
      "gitlab_ecosystem": ["auth_issues", "recent_discussions", "related_mrs"]
    },
    "investigation_type": "parallel",
    "coordination": "Combine structural insights with team context"
  }
}
```

#### 3. Adaptive Investigation Strategies

**Follow the Evidence**:
- Code Navigator finds suspicious function → Git History: when/why was it changed?
- Repository Explorer finds config issue → Code Navigator: how is config used?
- GitLab Ecosystem finds related bug → Code Navigator: is the fix complete?

**Go Deeper When Needed**:
- Initial findings are surface-level → Re-invoke same agent with specific focus
- Agent reports "complex system" → Invoke additional agents for comprehensive view
- Findings contradict each other → Investigate further to resolve

**Iterative Refinement**:
- Agent A finds X → Agent B investigates X → Agent A re-examines X with new context
- Findings reveal new questions → Additional rounds of investigation

#### 4. Investigation Types

Added investigation type classification:
- `initial`: First investigation of an area
- `follow-up`: Building on previous findings  
- `deep-dive`: Focused deep analysis of specific findings
- `parallel`: Multiple agents working simultaneously

## Files Modified

### 1. Orchestrator Prompt (`ai_gateway/prompts/definitions/workflow/context_2_0_orchestrator/system/1.0.0.jinja`)

**Phase 2: Agent Selection** (lines 53-73):
- ❌ Removed hardcoded selection guidelines
- ✅ Added query-driven selection principles
- ✅ Added parallel opportunities assessment
- ✅ Added dependency-aware routing

**Phase 3: Adaptive Investigation** (lines 75-102):
- ❌ Removed rigid routing principles
- ✅ Added evidence-driven strategies
- ✅ Added depth assessment criteria
- ✅ Added iterative refinement approaches

**Output Format** (lines 121-203):
- ✅ Added parallel agent routing schema
- ✅ Added investigation types
- ✅ Added concrete examples for different scenarios
- ✅ Added coordination planning for parallel agents

**Guidelines** (lines 212-227):
- ✅ Emphasized strategic thinking over predetermined sequences
- ✅ Added parallel routing capabilities
- ✅ Added adaptive depth requirements
- ✅ Added investigation strategy examples

### 2. Orchestrator Code (`duo_workflow_service/agents/context_2_0/orchestrator.py`)

**Router Function** (lines 316-368):
- ✅ Added support for parallel agent routing
- ✅ Enhanced JSON parsing for new schema
- ✅ Added logging for parallel routing decisions
- ✅ Prepared for future true parallel execution

**Routing Approach** (lines 187-218):
- ❌ Removed hardcoded sequential approach
- ✅ Added query-driven strategy examples
- ✅ Added parallel investigation patterns
- ✅ Added evidence-driven flow descriptions
- ✅ Added adaptive depth criteria

## Expected Behavior After Fix

### Example: "Fix authentication bug"

**Intelligent Orchestrator Decision**:
```json
{
  "routing_decision": {
    "next_agents": ["code_navigator", "gitlab_ecosystem"],
    "reasoning": "Can simultaneously locate auth code and check for related issues/discussions.",
    "focus_areas": {
      "code_navigator": ["authentication_service", "login_flow", "error_handling"],
      "gitlab_ecosystem": ["auth_issues", "recent_discussions", "related_mrs"]
    },
    "investigation_type": "parallel",
    "coordination": "Combine code analysis with team context"
  }
}
```

**Follow-up Based on Findings**:
If Code Navigator finds suspicious recent changes:
```json
{
  "routing_decision": {
    "next_agent": "git_history",
    "reasoning": "Code Navigator found suspicious auth changes - need to understand when/why they were made.",
    "focus_areas": ["auth_service_commits", "recent_changes", "bug_fix_history"],
    "investigation_type": "follow-up",
    "depends_on": ["code_navigator_findings"]
  }
}
```

### Example: "How does the prompt registry work?"

**Intelligent Orchestrator Decision**:
```json
{
  "routing_decision": {
    "next_agent": "repository_explorer",
    "reasoning": "Need to locate prompt registry files and understand project structure first.",
    "focus_areas": ["prompt_registry_location", "configuration", "architecture"],
    "investigation_type": "initial",
    "depends_on": []
  }
}
```

**Adaptive Follow-up**:
After Repository Explorer finds the registry:
```json
{
  "routing_decision": {
    "next_agent": "code_navigator",
    "reasoning": "Repository Explorer found the registry structure - now need deep code analysis.",
    "focus_areas": ["registry_class", "prompt_loading", "template_rendering", "caching"],
    "investigation_type": "follow-up",
    "depends_on": ["repository_explorer_findings"]
  }
}
```

## Benefits

1. **Query-Specific**: Routes based on what the query actually needs, not predetermined sequences
2. **Parallel Efficiency**: Can investigate multiple independent aspects simultaneously
3. **Adaptive Depth**: Goes deeper when findings warrant it, stays surface-level when appropriate
4. **Evidence-Driven**: Each routing decision builds on previous findings
5. **Iterative**: Can re-investigate areas with refined focus based on new insights
6. **Quality-Focused**: Routes to synthesis only when sufficient depth is achieved

## Current Limitations

1. **Parallel Execution**: Currently routes to first agent in parallel list (sequential fallback)
2. **State Management**: Doesn't yet store parallel routing context for future implementation
3. **Tool Coordination**: No mechanism for agents to share findings during parallel execution

## Future Enhancements

1. **True Parallel Execution**: Implement LangGraph parallel node execution
2. **Shared Context**: Allow parallel agents to share findings in real-time
3. **Dynamic Re-routing**: Mid-execution routing changes based on emerging findings
4. **Quality Metrics**: Route based on context quality thresholds, not just completion

## Status

✅ **FIXED** - Orchestrator now makes intelligent, adaptive routing decisions
✅ **TESTED** - JSON schema supports both single and parallel routing
✅ **DOCUMENTED** - Clear examples and strategies in prompt
✅ **PREPARED** - Code ready for future parallel execution implementation

The orchestrator is now truly LLM-driven and adaptive! 🧠🚀

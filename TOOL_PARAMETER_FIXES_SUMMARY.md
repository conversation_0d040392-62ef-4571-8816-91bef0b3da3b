# Tool Parameter Fixes Summary

## Overview

Fixed critical tool parameter issues in Context 2.0 specialist agents that were causing Pydantic validation errors. The root cause was that tools were being called with incorrect parameter names that didn't match their input schemas.

## Critical Architectural Issue Discovered

**ALL GitLab and Git tools require project_id, group_id, or other context parameters that are NOT available to the specialist agents.**

The `AgentInvestigationContext` only contains:
- `goal`: str
- `current_findings`: Dict[str, Any]
- `calling_agent`: Optional[str]
- `investigation_depth`: int
- `max_depth`: int
- `call_stack`: List[str]

It does NOT contain:
- `project_id` - Required by most GitLab tools
- `group_id` - Required by epic tools
- `previous_session_id` - Required by get_previous_session_context
- Any other project/repository context

## Files Fixed

### 1. gitlab-ai-gateway/********************/agents/context_2_0/gitlab_ecosystem.py

**Issues Found:**
1. Line 181: `get_project` - Called without required `project_id` parameter
2. Lines 275-278: `gitlab_issue_search` - Used `query` and `max_results` instead of `id`, `search`, `search_type`
3. Lines 288-291: `list_issues` - Called without required `project_id` parameter
4. Line 317: `get_issue` - Used `issue_id` instead of `project_id` + `issue_iid`
5. Line 324: `list_issue_notes` - Used `issue_id` instead of `project_id` + `issue_iid`
6. Lines 438-441: `gitlab_merge_request_search` - **TOOL DOESN'T EXIST!** Not implemented in search.py
7. Line 454: `get_merge_request` - Used `merge_request_id` instead of `project_id` + `merge_request_iid`
8. Line 560: `list_epics` - Used `max_results` instead of required `group_id`
9. Line 607: `get_previous_session_context` - Called without required `previous_session_id`

**Fixes Applied:**
- Commented out ALL tool calls that require missing context parameters
- Added detailed TODO comments explaining what parameters are needed
- Added stub return values to prevent crashes: `{"success": False, "content": None}`
- Documented the correct parameter names for each tool

### 2. gitlab-ai-gateway/********************/agents/context_2_0/git_history.py

**Status:** NOT YET FIXED

**Issues Identified:**
1. Line 182: `list_commits` - Requires `project_id` or `url`
2. Line 206: `get_commit` - Requires `project_id` + `commit_sha` (or `url`)
3. Line 400: `run_read_only_git_command` - Requires `repository_url` + `command` + optional `args`
4. Line 417: `get_commit` - Same as above
5. Line 434: `get_commit_diff` - Requires `project_id` + `commit_sha` (or `url`)
6. Line 576: `run_read_only_git_command` - Same as line 400

## Tool Parameter Reference

### GitLab Issue Tools
- `gitlab_issue_search`: Requires `id` (project/group ID), `search`, `search_type` ("projects" or "groups")
- `list_issues`: Requires `project_id` or `url`, optional: `state`, `sort`, `per_page`
- `get_issue`: Requires `project_id` + `issue_iid` (or `url`)
- `list_issue_notes`: Requires `project_id` + `issue_iid` (or `url`)

### GitLab Merge Request Tools
- `gitlab_merge_request_search`: **DOES NOT EXIST** - needs to be implemented
- `get_merge_request`: Requires `project_id` + `merge_request_iid` (or `url`)

### GitLab Project Tools
- `get_project`: Requires `project_id`

### GitLab Epic Tools
- `list_epics`: Requires `group_id` or `url`, optional: `page`, `per_page`, etc.
- `get_epic`: Requires `group_id` + `epic_id` (or `url`)

### GitLab Session Tools
- `get_previous_session_context`: Requires `previous_session_id`

### Git Commit Tools
- `list_commits`: Requires `project_id` or `url`, optional: `per_page`, `since`, `until`, etc.
- `get_commit`: Requires `project_id` + `commit_sha` (or `url`)
- `get_commit_diff`: Requires `project_id` + `commit_sha` (or `url`)

### Git Command Tools
- `run_read_only_git_command`: Requires `repository_url`, `command`, optional: `args`

## Recommended Solutions

### Option 1: Add Project Context to AgentInvestigationContext
```python
class AgentInvestigationContext(TypedDict):
    goal: str
    current_findings: Dict[str, Any]
    calling_agent: Optional[str]
    investigation_depth: int
    max_depth: int
    call_stack: List[str]
    # NEW FIELDS:
    project_id: Optional[str]  # GitLab project ID
    group_id: Optional[str]    # GitLab group ID
    repository_url: Optional[str]  # Git repository URL
    previous_session_id: Optional[int]  # For historical context
```

### Option 2: Extract Context from State
Modify specialist agents to extract project context from the workflow state before calling tools.

### Option 3: Use Default Project from Workflow
Configure a default project_id at the workflow level that all agents can access.

## Testing Required

After implementing one of the solutions above:

1. **Syntax Check**: ✅ All modified files compile successfully
2. **Service Reload**: ⚠️ Service needs to be restarted to load new code
3. **Integration Test**: Test in local GitLab instance Flow UI
4. **LangSmith Traces**: Verify no Pydantic validation errors
5. **Tool Execution**: Verify tools execute successfully with correct parameters

## Current Status

- ✅ `gitlab_ecosystem.py`: All tool calls commented out with detailed TODOs
- ✅ `repository_explorer.py`: Fixed 5 tool calls (previous work)
- ✅ `code_navigator.py`: Fixed 3 tool calls (previous work)
- ❌ `git_history.py`: Needs same treatment as gitlab_ecosystem.py
- ❌ **Service not reloaded**: Error still shows old code at line 313

## Next Steps

1. Fix `git_history.py` tool calls (comment out with TODOs)
2. Implement one of the recommended solutions to provide project context
3. Restart duo-workflow-service to load new code
4. Test in local GitLab instance
5. Verify LangSmith traces show successful execution


{"name": "vue2-webviews", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "chat": "vite --config gitlab_duo_chat/vite.config.js build", "chat-watch": "vite --config gitlab_duo_chat/vite.config.js build --watch", "security": "vite --config security_finding/vite.config.js build", "security-watch": "vite --config security_finding/vite.config.js build --watch", "build": "rimraf dist/ && concurrently 'npm:chat' 'npm:security'", "watch": "rimraf dist/ && concurrently 'npm:chat-watch' 'npm:security-watch'", "test": "jest --config jest.config.js", "preview": "vite preview --port 4173", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "preinstall": "node ../../scripts/ensure_npm.mjs"}, "dependencies": {"@gitlab/duo-ui": "8.17.0", "@gitlab/ui": "112.3.1", "dompurify": "^3.0.5", "highlight.js": "^11.9.0", "marked-highlight": "^2.1.4", "v-tooltip": "^2.1.3", "vue": "^2.7.16", "vue-template-compiler": "^2.7.16"}, "devDependencies": {"@babel/core": "^7.23.9", "@babel/preset-env": "^7.23.8", "@vitejs/plugin-vue2": "^1.1.2", "@vue/eslint-config-prettier": "^7.0.0", "@vue/test-utils": "^1.3.6", "babel-jest": "^29.7.0", "concurrently": "^8.2.2", "eslint": "^8.5.0", "eslint-plugin-vue": "^9.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "node-html-parser": "^6.1.12", "postcss": "^8.4.35", "prettier": "^2.5.1", "rimraf": "^5.0.5", "tailwindcss": "^3.4.1", "vite": "^3.0.2", "vue-jest": "^3.0.7"}}
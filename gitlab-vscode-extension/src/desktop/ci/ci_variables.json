[{"name": "CHAT_CHANNEL", "description": "The Source chat channel that triggered the [ChatOps](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../chatops/_index.md) command."}, {"name": "CHAT_INPUT", "description": "The additional arguments passed with the [ChatOps](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../chatops/_index.md) command."}, {"name": "CHAT_USER_ID", "description": "The chat service's user ID of the user who triggered the [ChatOps](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../chatops/_index.md) command."}, {"name": "CI", "description": "Available for all jobs executed in CI/CD. `true` when available."}, {"name": "CI_API_GRAPHQL_URL", "description": "The GitLab API GraphQL root URL. Introduced in GitLab 15.11."}, {"name": "CI_API_V4_URL", "description": "The GitLab API v4 root URL."}, {"name": "CI_BUILDS_DIR", "description": "The top-level directory where builds are executed."}, {"name": "CI_COMMIT_AUTHOR", "description": "The author of the commit in `Name <email>` format."}, {"name": "CI_COMMIT_BEFORE_SHA", "description": "The previous latest commit present on a branch or tag. Is always `0000000000000000000000000000000000000000` for merge request pipelines, scheduled pipelines, the first commit in pipelines for branches or tags, or when manually running a pipeline."}, {"name": "CI_COMMIT_BRANCH", "description": "The commit branch name. Available in branch pipelines, including pipelines for the default branch. Not available in merge request pipelines or tag pipelines."}, {"name": "CI_COMMIT_DESCRIPTION", "description": "The description of the commit. If the title is shorter than 100 characters, the message without the first line."}, {"name": "CI_COMMIT_MESSAGE", "description": "The full commit message."}, {"name": "CI_COMMIT_REF_NAME", "description": "The branch or tag name for which project is built."}, {"name": "CI_COMMIT_REF_PROTECTED", "description": "`true` if the job is running for a protected reference, `false` otherwise."}, {"name": "CI_COMMIT_REF_SLUG", "description": "`CI_COMMIT_REF_NAME` in lowercase, shortened to 63 bytes, and with everything except `0-9` and `a-z` replaced with `-`. No leading / trailing `-`. Use in URLs, host names and domain names."}, {"name": "CI_COMMIT_SHA", "description": "The commit revision the project is built for."}, {"name": "CI_COMMIT_SHORT_SHA", "description": "The first eight characters of `CI_COMMIT_SHA`."}, {"name": "CI_COMMIT_TAG", "description": "The commit tag name. Available only in pipelines for tags."}, {"name": "CI_COMMIT_TAG_MESSAGE", "description": "The commit tag message. Available only in pipelines for tags. Introduced in GitLab 15.5."}, {"name": "CI_COMMIT_TIMESTAMP", "description": "The timestamp of the commit in the [ISO 8601](https://www.rfc-editor.org/rfc/rfc3339#appendix-A) format. For example, `2022-01-31T16:47:55Z`. [UTC by default](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../../administration/timezone.md)."}, {"name": "CI_COMMIT_TITLE", "description": "The title of the commit. The full first line of the message."}, {"name": "CI_CONCURRENT_ID", "description": "The unique ID of build execution in a single executor."}, {"name": "CI_CONCURRENT_PROJECT_ID", "description": "The unique ID of build execution in a single executor and project."}, {"name": "CI_CONFIG_PATH", "description": "The path to the CI/CD configuration file. Defaults to `.gitlab-ci.yml`."}, {"name": "CI_DEBUG_SERVICES", "description": "`true` if [service container logging](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../services/_index.md#capturing-service-container-logs) is enabled. Introduced in GitLab 15.7. Requires GitLab Runner 15.7."}, {"name": "CI_DEBUG_TRACE", "description": "`true` if [debug logging (tracing)](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/variables_troubleshooting.md#enable-debug-logging) is enabled."}, {"name": "CI_DEFAULT_BRANCH", "description": "The name of the project's default branch."}, {"name": "CI_DEFAULT_BRANCH_SLUG", "description": "`CI_DEFAULT_BRANCH` in lowercase, shortened to 63 bytes, and with everything except `0-9` and `a-z` replaced with `-`. No leading / trailing `-`. Use in URLs, host names and domain names."}, {"name": "CI_DEPENDENCY_PROXY_DIRECT_GROUP_IMAGE_PREFIX", "description": "The direct group image prefix for pulling images through the Dependency Proxy."}, {"name": "CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX", "description": "The top-level group image prefix for pulling images through the Dependency Proxy."}, {"name": "CI_DEPENDENCY_PROXY_PASSWORD", "description": "The password to pull images through the Dependency Proxy."}, {"name": "CI_DEPENDENCY_PROXY_SERVER", "description": "The server for logging in to the Dependency Proxy. This variable is equivalent to `$CI_SERVER_HOST:$CI_SERVER_PORT`."}, {"name": "CI_DEPENDENCY_PROXY_USER", "description": "The username to pull images through the Dependency Proxy."}, {"name": "CI_DEPLOY_FREEZE", "description": "Only available if the pipeline runs during a [deploy freeze window](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../../user/project/releases/_index.md#prevent-unintentional-releases-by-setting-a-deploy-freeze). `true` when available."}, {"name": "CI_DEPLOY_PASSWORD", "description": "The authentication password of the [GitLab Deploy Token](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../../user/project/deploy_tokens/_index.md#gitlab-deploy-token), if the project has one."}, {"name": "CI_DEPLOY_USER", "description": "The authentication username of the [GitLab Deploy Token](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../../user/project/deploy_tokens/_index.md#gitlab-deploy-token), if the project has one."}, {"name": "CI_DISPOSABLE_ENVIRONMENT", "description": "Only available if the job is executed in a disposable environment (something that is created only for this job and disposed of/destroyed after the execution - all executors except `shell` and `ssh`). `true` when available."}, {"name": "CI_ENVIRONMENT_ACTION", "description": "The action annotation specified for this job's environment. Available if [`environment:action`](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../yaml/_index.md#environmentaction) is set. Can be `start`, `prepare`, or `stop`."}, {"name": "CI_ENVIRONMENT_NAME", "description": "The name of the environment for this job. Available if [`environment:name`](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../yaml/_index.md#environmentname) is set."}, {"name": "CI_ENVIRONMENT_SLUG", "description": "The simplified version of the environment name, suitable for inclusion in DNS, URLs, Kubernetes labels, and so on. Available if [`environment:name`](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../yaml/_index.md#environmentname) is set. The slug is [truncated to 24 characters](https://gitlab.com/gitlab-org/gitlab/-/issues/20941). A random suffix is automatically added to [uppercase environment names](https://gitlab.com/gitlab-org/gitlab/-/issues/415526)."}, {"name": "CI_ENVIRONMENT_TIER", "description": "The [deployment tier of the environment](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../environments/_index.md#deployment-tier-of-environments) for this job."}, {"name": "CI_ENVIRONMENT_URL", "description": "The URL of the environment for this job. Available if [`environment:url`](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../yaml/_index.md#environmenturl) is set."}, {"name": "CI_GITLAB_FIPS_MODE", "description": "Only available if [FIPS mode](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../../development/fips_gitlab.md) is enabled in the GitLab instance. `true` when available."}, {"name": "CI_HAS_OPEN_REQUIREMENTS", "description": "Only available if the pipeline's project has an open [requirement](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../../user/project/requirements/_index.md). `true` when available."}, {"name": "CI_JOB_GROUP_NAME", "description": "The shared name of a group of jobs, when using either [`parallel`](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../yaml/_index.md#parallel) or [manually grouped jobs](../jobs/_index.md#group-similar-jobs-together-in-pipeline-views). For example, if the job name is `rspec:test: [ruby, ubuntu]`, the `CI_JOB_GROUP_NAME` is `rspec:test`. It is the same as `CI_JOB_NAME` otherwise. Introduced in GitLab 17.10."}, {"name": "CI_JOB_ID", "description": "The internal ID of the job, unique across all jobs in the GitLab instance."}, {"name": "CI_JOB_IMAGE", "description": "The name of the <PERSON><PERSON> image running the job."}, {"name": "CI_JOB_MANUAL", "description": "Only available if the job was started manually. `true` when available."}, {"name": "CI_JOB_NAME", "description": "The name of the job."}, {"name": "CI_JOB_NAME_SLUG", "description": "`CI_JOB_NAME` in lowercase, shortened to 63 bytes, and with everything except `0-9` and `a-z` replaced with `-`. No leading / trailing `-`. Use in paths. Introduced in GitLab 15.4."}, {"name": "CI_JOB_STAGE", "description": "The name of the job's stage."}, {"name": "CI_JOB_STARTED_AT", "description": "The date and time when a job started, in [ISO 8601](https://www.rfc-editor.org/rfc/rfc3339#appendix-A) format. For example, `2022-01-31T16:47:55Z`. [UTC by default](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../../administration/timezone.md)."}, {"name": "CI_JOB_STATUS", "description": "The status of the job as each runner stage is executed. Use with [`after_script`](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../yaml/_index.md#after_script). Can be `success`, `failed`, or `canceled`."}, {"name": "CI_JOB_TIMEOUT", "description": "The job timeout, in seconds. Introduced in GitLab 15.7. Requires GitLab Runner 15.7."}, {"name": "CI_JOB_TOKEN", "description": "A token to authenticate with [certain API endpoints](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../jobs/ci_job_token.md). The token is valid as long as the job is running."}, {"name": "CI_JOB_URL", "description": "The job details URL."}, {"name": "CI_KUBERNETES_ACTIVE", "description": "Only available if the pipeline has a Kubernetes cluster available for deployments. `true` when available."}, {"name": "CI_NODE_INDEX", "description": "The index of the job in the job set. Only available if the job uses [`parallel`](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../yaml/_index.md#parallel)."}, {"name": "CI_NODE_TOTAL", "description": "The total number of instances of this job running in parallel. Set to `1` if the job does not use [`parallel`](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../yaml/_index.md#parallel)."}, {"name": "CI_OPEN_MERGE_REQUESTS", "description": "A comma-separated list of up to four merge requests that use the current branch and project as the merge request source. Only available in branch and merge request pipelines if the branch has an associated merge request. For example, `gitlab-org/gitlab!333,gitlab-org/gitlab-foss!11`."}, {"name": "CI_PAGES_DOMAIN", "description": "The instance's domain that hosts GitLab Pages, not including the namespace subdomain. To use the full hostname, use `CI_PAGES_HOSTNAME` instead."}, {"name": "CI_PAGES_HOSTNAME", "description": "The full hostname of the Pages deployment."}, {"name": "CI_PAGES_URL", "description": "The URL for a GitLab Pages site. Always a subdomain of `CI_PAGES_DOMAIN`. In GitLab 17.9 and later, the value includes the `path_prefix` when one is specified."}, {"name": "CI_PIPELINE_CREATED_AT", "description": "The date and time when the pipeline was created, in [ISO 8601](https://www.rfc-editor.org/rfc/rfc3339#appendix-A) format. For example, `2022-01-31T16:47:55Z`. [UTC by default](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../../administration/timezone.md)."}, {"name": "CI_PIPELINE_ID", "description": "The instance-level ID of the current pipeline. This ID is unique across all projects on the GitLab instance."}, {"name": "CI_PIPELINE_IID", "description": "The project-level IID (internal ID) of the current pipeline. This ID is unique only in the current project."}, {"name": "CI_PIPELINE_NAME", "description": "The pipeline name defined in [`workflow:name`](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../yaml/_index.md#workflowname). Introduced in GitLab 16.3."}, {"name": "CI_PIPELINE_SCHEDULE_DESCRIPTION", "description": "The description of the pipeline schedule. Only available in scheduled pipelines. Introduced in GitLab 17.8."}, {"name": "CI_PIPELINE_SOURCE", "description": "How the pipeline was triggered. The value can be one of the [pipeline sources](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../jobs/job_rules.md#ci_pipeline_source-predefined-variable)."}, {"name": "CI_PIPELINE_TRIGGERED", "description": "`true` if the job was [triggered](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../triggers/_index.md)."}, {"name": "CI_PIPELINE_URL", "description": "The URL for the pipeline details."}, {"name": "CI_PROJECT_CLASSIFICATION_LABEL", "description": "The project [external authorization classification label](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../../administration/settings/external_authorization.md)."}, {"name": "CI_PROJECT_DESCRIPTION", "description": "The project description as displayed in the GitLab web interface. Introduced in GitLab 15.1."}, {"name": "CI_PROJECT_DIR", "description": "The full path the repository is cloned to, and where the job runs from. If the GitLab Runner `builds_dir` parameter is set, this variable is set relative to the value of `builds_dir`. For more information, see the [Advanced GitLab Runner configuration](https://docs.gitlab.com/runner/configuration/advanced-configuration.html#the-runners-section)."}, {"name": "CI_PROJECT_ID", "description": "The ID of the current project. This ID is unique across all projects on the GitLab instance."}, {"name": "CI_PROJECT_NAME", "description": "The name of the directory for the project. For example if the project URL is `gitlab.example.com/group-name/project-1`, `CI_PROJECT_NAME` is `project-1`."}, {"name": "CI_PROJECT_NAMESPACE", "description": "The project namespace (username or group name) of the job."}, {"name": "CI_PROJECT_NAMESPACE_ID", "description": "The project namespace ID of the job. Introduced in GitLab 15.7."}, {"name": "CI_PROJECT_NAMESPACE_SLUG", "description": "`$CI_PROJECT_NAMESPACE` in lowercase with characters that are not `a-z` or `0-9` replaced with - and shortened to 63 bytes."}, {"name": "CI_PROJECT_PATH", "description": "The project namespace with the project name included."}, {"name": "CI_PROJECT_PATH_SLUG", "description": "`$CI_PROJECT_PATH` in lowercase with characters that are not `a-z` or `0-9` replaced with `-` and shortened to 63 bytes. Use in URLs and domain names."}, {"name": "CI_PROJECT_REPOSITORY_LANGUAGES", "description": "A comma-separated, lowercase list of the languages used in the repository. For example `ruby,javascript,html,css`. The maximum number of languages is limited to 5. An issue [proposes to increase the limit](https://gitlab.com/gitlab-org/gitlab/-/issues/368925)."}, {"name": "CI_PROJECT_ROOT_NAMESPACE", "description": "The root project namespace (username or group name) of the job. For example, if `CI_PROJECT_NAMESPACE` is `root-group/child-group/grandchild-group`, `CI_PROJECT_ROOT_NAMESPACE` is `root-group`."}, {"name": "CI_PROJECT_TITLE", "description": "The human-readable project name as displayed in the GitLab web interface."}, {"name": "CI_PROJECT_URL", "description": "The HTTP(S) address of the project."}, {"name": "CI_PROJECT_VISIBILITY", "description": "The project visibility. Can be `internal`, `private`, or `public`."}, {"name": "CI_REGISTRY", "description": "Address of the [container registry](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../../user/packages/container_registry/_index.md) server, formatted as `<host>[:<port>]`. For example: `registry.gitlab.example.com`. Only available if the container registry is enabled for the GitLab instance."}, {"name": "CI_REGISTRY_IMAGE", "description": "Base address for the container registry to push, pull, or tag project's images, formatted as `<host>[:<port>]/<project_full_path>`. For example: `registry.gitlab.example.com/my_group/my_project`. Image names must follow the [container registry naming convention](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../../user/packages/container_registry/_index.md#naming-convention-for-your-container-images). Only available if the container registry is enabled for the project."}, {"name": "CI_REGISTRY_PASSWORD", "description": "The password to push containers to the GitLab project's container registry. Only available if the container registry is enabled for the project. This password value is the same as the `CI_JOB_TOKEN` and is valid only as long as the job is running. Use the `CI_DEPLOY_PASSWORD` for long-lived access to the registry"}, {"name": "CI_REGISTRY_USER", "description": "The username to push containers to the project's GitLab container registry. Only available if the container registry is enabled for the project."}, {"name": "CI_RELEASE_DESCRIPTION", "description": "The description of the release. Available only on pipelines for tags. Description length is limited to first 1024 characters. Introduced in GitLab 15.5."}, {"name": "CI_REPOSITORY_URL", "description": "The full path to Git clone (HTTP) the repository with a [CI/CD job token](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../jobs/ci_job_token.md), in the format `https://gitlab-ci-token:$<EMAIL>/my-group/my-project.git`."}, {"name": "CI_RUNNER_DESCRIPTION", "description": "The description of the runner."}, {"name": "CI_RUNNER_EXECUTABLE_ARCH", "description": "The OS/architecture of the GitLab Runner executable. Might not be the same as the environment of the executor."}, {"name": "CI_RUNNER_ID", "description": "The unique ID of the runner being used."}, {"name": "CI_RUNNER_REVISION", "description": "The revision of the runner running the job."}, {"name": "CI_RUNNER_SHORT_TOKEN", "description": "The runner's unique ID, used to authenticate new job requests. The token contains a prefix, and the first 17 characters are used."}, {"name": "CI_RUNNER_TAGS", "description": "A JSON array of runner tags. For example `[\"tag_1\", \"tag_2\"]`."}, {"name": "CI_RUNNER_VERSION", "description": "The version of the GitLab Runner running the job."}, {"name": "CI_SERVER", "description": "Available for all jobs executed in CI/CD. `yes` when available."}, {"name": "CI_SERVER_FQDN", "description": "The fully qualified domain name (FQDN) of the instance. For example `gitlab.example.com:8080`. Introduced in GitLab 16.10."}, {"name": "CI_SERVER_HOST", "description": "The host of the GitLab instance URL, without protocol or port. For example `gitlab.example.com`."}, {"name": "CI_SERVER_NAME", "description": "The name of CI/CD server that coordinates jobs."}, {"name": "CI_SERVER_PORT", "description": "The port of the GitLab instance URL, without host or protocol. For example `8080`."}, {"name": "CI_SERVER_PROTOCOL", "description": "The protocol of the GitLab instance URL, without host or port. For example `https`."}, {"name": "CI_SERVER_REVISION", "description": "GitLab revision that schedules jobs."}, {"name": "CI_SERVER_SHELL_SSH_HOST", "description": "The SSH host of the GitLab instance, used for access to Git repositories through SSH. For example `gitlab.com`. Introduced in GitLab 15.11."}, {"name": "CI_SERVER_SHELL_SSH_PORT", "description": "The SSH port of the GitLab instance, used for access to Git repositories through SSH. For example `22`. Introduced in GitLab 15.11."}, {"name": "CI_SERVER_TLS_CA_FILE", "description": "File containing the TLS CA certificate to verify the GitLab server when `tls-ca-file` set in [runner settings](https://docs.gitlab.com/runner/configuration/advanced-configuration.html#the-runners-section)."}, {"name": "CI_SERVER_TLS_CERT_FILE", "description": "File containing the TLS certificate to verify the GitLab server when `tls-cert-file` set in [runner settings](https://docs.gitlab.com/runner/configuration/advanced-configuration.html#the-runners-section)."}, {"name": "CI_SERVER_TLS_KEY_FILE", "description": "File containing the TLS key to verify the GitLab server when `tls-key-file` set in [runner settings](https://docs.gitlab.com/runner/configuration/advanced-configuration.html#the-runners-section)."}, {"name": "CI_SERVER_URL", "description": "The base URL of the GitLab instance, including protocol and port. For example `https://gitlab.example.com:8080`."}, {"name": "CI_SERVER_VERSION", "description": "The full version of the GitLab instance."}, {"name": "CI_SERVER_VERSION_MAJOR", "description": "The major version of the GitLab instance. For example, if the GitLab version is `17.2.1`, the `CI_SERVER_VERSION_MAJOR` is `17`."}, {"name": "CI_SERVER_VERSION_MINOR", "description": "The minor version of the GitLab instance. For example, if the GitLab version is `17.2.1`, the `CI_SERVER_VERSION_MINOR` is `2`."}, {"name": "CI_SERVER_VERSION_PATCH", "description": "The patch version of the GitLab instance. For example, if the GitLab version is `17.2.1`, the `CI_SERVER_VERSION_PATCH` is `1`."}, {"name": "CI_SHARED_ENVIRONMENT", "description": "Only available if the job is executed in a shared environment (something that is persisted across CI/CD invocations, like the `shell` or `ssh` executor). `true` when available."}, {"name": "CI_TEMPLATE_REGISTRY_HOST", "description": "The host of the registry used by CI/CD templates. Defaults to `registry.gitlab.com`. Introduced in GitLab 15.3."}, {"name": "CI_TRIGGER_SHORT_TOKEN", "description": "First 4 characters of the [trigger token](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../triggers/_index.md#create-a-pipeline-trigger-token) of the current job. Only available if the pipeline was [triggered with a trigger token](../triggers/_index.md). For example, for a trigger token of `glptt-1234567890abcdefghij`, `CI_TRIGGER_SHORT_TOKEN` would be `1234`. Introduced in GitLab 17.0. <!-- gitleaks:allow -->"}, {"name": "GITLAB_CI", "description": "Available for all jobs executed in CI/CD. `true` when available."}, {"name": "GITLAB_FEATURES", "description": "The comma-separated list of licensed features available for the GitLab instance and license."}, {"name": "GITLAB_USER_EMAIL", "description": "The email of the user who started the pipeline, unless the job is a manual job. In manual jobs, the value is the email of the user who started the job."}, {"name": "GITLAB_USER_ID", "description": "The numeric ID of the user who started the pipeline, unless the job is a manual job. In manual jobs, the value is the ID of the user who started the job."}, {"name": "GITLAB_USER_LOGIN", "description": "The unique username of the user who started the pipeline, unless the job is a manual job. In manual jobs, the value is the username of the user who started the job."}, {"name": "GITLAB_USER_NAME", "description": "The display name (user-defined **Full name** in the profile settings) of the user who started the pipeline, unless the job is a manual job. In manual jobs, the value is the name of the user who started the job."}, {"name": "KUBECONFIG", "description": "The path to the `kubeconfig` file with contexts for every shared agent connection. Only available when a [GitLab agent is authorized to access the project](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../../user/clusters/agent/ci_cd_workflow.md#authorize-agent-access)."}, {"name": "TRIGGER_PAYLOAD", "description": "The webhook payload. Only available when a pipeline is [triggered with a webhook](https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/ci/variables/../triggers/_index.md#access-webhook-payload)."}]
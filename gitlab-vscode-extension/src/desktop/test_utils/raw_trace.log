[0KRunning with gitlab-runner 15.4.0~beta.5.gdefc7017 (defc7017)[0;m
[0K  on green-5.shared.runners-manager.gitlab.com/default xS6Vzpvo[0;m
section_start:1665504701:prepare_executor
[0K[0K[36;1mPreparing the "docker+machine" executor[0;m[0;m
[0KUsing Docker executor with image mcr.microsoft.com/dotnet/core/sdk:latest ...[0;m
[0KPulling docker image mcr.microsoft.com/dotnet/core/sdk:latest ...[0;m
[0KUsing docker image sha256:d835d6974098e03f902ea006ca6ae274bc8ee825210b57d2794a7fbe10c26a8a for mcr.microsoft.com/dotnet/core/sdk:latest with digest mcr.microsoft.com/dotnet/core/sdk@sha256:a854fd8ff82fa9cc5284007d7c4357cf786e44ac2e59bbe83f13c56bb234dc11 ...[0;m
section_end:1665504724:prepare_executor
[0Ksection_start:1665504724:prepare_script
[0K[0K[36;1mPreparing environment[0;m[0;m
Running on runner-xs6vzpvo-project-40127262-concurrent-0 via runner-xs6vzpvo-shared-1665503996-566f8f8a...
section_end:1665504727:prepare_script
[0Ksection_start:1665504727:get_sources
[0K[0K[36;1mGetting source from Git repository[0;m[0;m
[32;1m$ eval "$CI_PRE_CLONE_SCRIPT"[0;m
[32;1mFetching changes with git depth set to 20...[0;m
Initialized empty Git repository in /builds/X_Sheep/dotnet-test-ci/.git/
[32;1mCreated fresh repository.[0;m
[32;1mChecking out 22b2937a as some-branch...[0;m

[32;1mSkipping Git submodules setup[0;m
section_end:1665504728:get_sources
[0Ksection_start:1665504728:download_artifacts
[0K[0K[36;1mDownloading artifacts[0;m[0;m
[32;1mDownloading artifacts for build (3157479511)...[0;m
Downloading artifacts from coordinator... ok      [0;m  id[0;m=3157479511 responseStatus[0;m=200 OK token[0;m=yXLScTfi
section_end:1665504729:download_artifacts
[0Ksection_start:1665504729:step_script
[0K[0K[36;1mExecuting "step_script" stage of the job script[0;m[0;m
[0KUsing docker image sha256:d835d6974098e03f902ea006ca6ae274bc8ee825210b57d2794a7fbe10c26a8a for mcr.microsoft.com/dotnet/core/sdk:latest with digest mcr.microsoft.com/dotnet/core/sdk@sha256:a854fd8ff82fa9cc5284007d7c4357cf786e44ac2e59bbe83f13c56bb234dc11 ...[0;m
[32;1m$ dotnet run[0;m
[38;2;0;0;0m█[38;2;3;0;0m█[38;2;6;0;0m█[38;2;9;0;0m█[38;2;12;0;0m█[38;2;15;0;0m█[38;2;18;0;0m█[38;2;21;0;0m█[38;2;24;0;0m█[38;2;27;0;0m█[38;2;30;0;0m█[38;2;33;0;0m█[38;2;36;0;0m█[38;2;39;0;0m█[38;2;42;0;0m█[38;2;45;0;0m█[38;2;48;0;0m█[38;2;51;0;0m█[38;2;54;0;0m█[38;2;57;0;0m█[38;2;60;0;0m█[38;2;63;0;0m█[38;2;66;0;0m█[38;2;69;0;0m█[38;2;72;0;0m█[38;2;75;0;0m█[38;2;78;0;0m█[38;2;81;0;0m█[38;2;84;0;0m█[38;2;87;0;0m█[38;2;90;0;0m█[38;2;93;0;0m█[38;2;96;0;0m█[38;2;99;0;0m█[38;2;102;0;0m█[38;2;105;0;0m█[38;2;108;0;0m█[38;2;111;0;0m█[38;2;114;0;0m█[38;2;117;0;0m█[38;2;120;0;0m█[38;2;123;0;0m█[38;2;126;0;0m█[38;2;129;0;0m█[38;2;132;0;0m█[38;2;135;0;0m█[38;2;138;0;0m█[38;2;141;0;0m█[38;2;144;0;0m█[38;2;147;0;0m█[38;2;150;0;0m█[38;2;153;0;0m█[38;2;156;0;0m█[38;2;159;0;0m█[38;2;162;0;0m█[38;2;165;0;0m█[38;2;168;0;0m█[38;2;171;0;0m█[38;2;174;0;0m█[38;2;177;0;0m█[38;2;180;0;0m█[38;2;183;0;0m█[38;2;186;0;0m█[38;2;189;0;0m█[38;2;192;0;0m█[38;2;195;0;0m█[38;2;198;0;0m█[38;2;201;0;0m█[38;2;204;0;0m█[38;2;207;0;0m█[38;2;210;0;0m█[38;2;213;0;0m█[38;2;216;0;0m█[38;2;219;0;0m█[38;2;222;0;0m█[38;2;225;0;0m█[38;2;228;0;0m█[38;2;231;0;0m█[38;2;234;0;0m█[38;2;237;0;0m█[38;2;240;0;0m█[38;2;243;0;0m█[38;2;246;0;0m█[38;2;249;0;0m█[38;2;252;0;0m█[38;2;255;0;0m█
[38;2;0;0;0m█[38;2;0;3;0m█[38;2;0;6;0m█[38;2;0;9;0m█[38;2;0;12;0m█[38;2;0;15;0m█[38;2;0;18;0m█[38;2;0;21;0m█[38;2;0;24;0m█[38;2;0;27;0m█[38;2;0;30;0m█[38;2;0;33;0m█[38;2;0;36;0m█[38;2;0;39;0m█[38;2;0;42;0m█[38;2;0;45;0m█[38;2;0;48;0m█[38;2;0;51;0m█[38;2;0;54;0m█[38;2;0;57;0m█[38;2;0;60;0m█[38;2;0;63;0m█[38;2;0;66;0m█[38;2;0;69;0m█[38;2;0;72;0m█[38;2;0;75;0m█[38;2;0;78;0m█[38;2;0;81;0m█[38;2;0;84;0m█[38;2;0;87;0m█[38;2;0;90;0m█[38;2;0;93;0m█[38;2;0;96;0m█[38;2;0;99;0m█[38;2;0;102;0m█[38;2;0;105;0m█[38;2;0;108;0m█[38;2;0;111;0m█[38;2;0;114;0m█[38;2;0;117;0m█[38;2;0;120;0m█[38;2;0;123;0m█[38;2;0;126;0m█[38;2;0;129;0m█[38;2;0;132;0m█[38;2;0;135;0m█[38;2;0;138;0m█[38;2;0;141;0m█[38;2;0;144;0m█[38;2;0;147;0m█[38;2;0;150;0m█[38;2;0;153;0m█[38;2;0;156;0m█[38;2;0;159;0m█[38;2;0;162;0m█[38;2;0;165;0m█[38;2;0;168;0m█[38;2;0;171;0m█[38;2;0;174;0m█[38;2;0;177;0m█[38;2;0;180;0m█[38;2;0;183;0m█[38;2;0;186;0m█[38;2;0;189;0m█[38;2;0;192;0m█[38;2;0;195;0m█[38;2;0;198;0m█[38;2;0;201;0m█[38;2;0;204;0m█[38;2;0;207;0m█[38;2;0;210;0m█[38;2;0;213;0m█[38;2;0;216;0m█[38;2;0;219;0m█[38;2;0;222;0m█[38;2;0;225;0m█[38;2;0;228;0m█[38;2;0;231;0m█[38;2;0;234;0m█[38;2;0;237;0m█[38;2;0;240;0m█[38;2;0;243;0m█[38;2;0;246;0m█[38;2;0;249;0m█[38;2;0;252;0m█[38;2;0;255;0m█
[38;2;0;0;0m█[38;2;0;0;3m█[38;2;0;0;6m█[38;2;0;0;9m█[38;2;0;0;12m█[38;2;0;0;15m█[38;2;0;0;18m█[38;2;0;0;21m█[38;2;0;0;24m█[38;2;0;0;27m█[38;2;0;0;30m█[38;2;0;0;33m█[38;2;0;0;36m█[38;2;0;0;39m█[38;2;0;0;42m█[38;2;0;0;45m█[38;2;0;0;48m█[38;2;0;0;51m█[38;2;0;0;54m█[38;2;0;0;57m█[38;2;0;0;60m█[38;2;0;0;63m█[38;2;0;0;66m█[38;2;0;0;69m█[38;2;0;0;72m█[38;2;0;0;75m█[38;2;0;0;78m█[38;2;0;0;81m█[38;2;0;0;84m█[38;2;0;0;87m█[38;2;0;0;90m█[38;2;0;0;93m█[38;2;0;0;96m█[38;2;0;0;99m█[38;2;0;0;102m█[38;2;0;0;105m█[38;2;0;0;108m█[38;2;0;0;111m█[38;2;0;0;114m█[38;2;0;0;117m█[38;2;0;0;120m█[38;2;0;0;123m█[38;2;0;0;126m█[38;2;0;0;129m█[38;2;0;0;132m█[38;2;0;0;135m█[38;2;0;0;138m█[38;2;0;0;141m█[38;2;0;0;144m█[38;2;0;0;147m█[38;2;0;0;150m█[38;2;0;0;153m█[38;2;0;0;156m█[38;2;0;0;159m█[38;2;0;0;162m█[38;2;0;0;165m█[38;2;0;0;168m█[38;2;0;0;171m█[38;2;0;0;174m█[38;2;0;0;177m█[38;2;0;0;180m█[38;2;0;0;183m█[38;2;0;0;186m█[38;2;0;0;189m█[38;2;0;0;192m█[38;2;0;0;195m█[38;2;0;0;198m█[38;2;0;0;201m█[38;2;0;0;204m█[38;2;0;0;207m█[38;2;0;0;210m█[38;2;0;0;213m█[38;2;0;0;216m█[38;2;0;0;219m█[38;2;0;0;222m█[38;2;0;0;225m█[38;2;0;0;228m█[38;2;0;0;231m█[38;2;0;0;234m█[38;2;0;0;237m█[38;2;0;0;240m█[38;2;0;0;243m█[38;2;0;0;246m█[38;2;0;0;249m█[38;2;0;0;252m█[38;2;0;0;255m█

[30mblack[0m    [90mbright black[0m     [40mblack[0m    [100mbright black[0m
[31mred[0m      [91mbright red[0m       [41mred[0m      [101mbright red[0m
[32mgreen[0m    [92mbright green[0m     [42mgreen[0m    [102mbright green[0m
[33myellow[0m   [93mbright yellow[0m    [43myellow[0m   [103mbright yellow[0m
[34mblue[0m     [94mbright blue[0m      [44mblue[0m     [104mbright blue[0m
[35mmagenta[0m  [95mbright magenta[0m   [45mmagenta[0m  [105mbright magenta[0m
[36mcyan[0m     [96mbright cyan[0m      [46mcyan[0m     [106mbright cyan[0m
[37mwhite[0m    [97mbright white[0m     [47mwhite[0m    [107mbright white[0m

[1mbold[0m [2mdim[0m [3mitalic[3m [4munderline[4m
[m
Doing some task... 0%
Doing some task... 4%
Doing some task... 8%
Doing some task... 12%
Doing some task... 16%
Doing some task... 20%
Doing some task... 24%
Doing some task... 28%
Doing some task... 32%
Doing some task... 36%
Doing some task... 40%
Doing some task... 44%
Doing some task... 48%
Doing some task... 52%
Doing some task... 56%
Doing some task... 60%
Doing some task... 64%
Doing some task... 68%
Doing some task... 72%
Doing some task... 76%
Doing some task... 80%
Doing some task... 84%
Doing some task... 88%
Doing some task... 92%
Doing some task... 96%
Doing some task... 100%
Task complete
section_end:1665504746:step_script
[0Ksection_start:1665504746:cleanup_file_variables
[0K[0K[36;1mCleaning up project directory and file based variables[0;m[0;m
section_end:1665504747:cleanup_file_variables
[0K[32;1mJob succeeded[0;m

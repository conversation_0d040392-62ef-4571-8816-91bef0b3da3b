2025-04-22T20:52:13.620313Z 00O [0KRunning with gitlab-runner 17.10.0~pre.41.g5c23fd8e (5c23fd8e)[0;m
2025-04-22T20:52:13.620319Z 00O [0K  on blue-3.saas-linux-small-amd64.runners-manager.gitlab.com/default zxwgkjAP, system ID: s_d5d3abbdfd0a[0;m
2025-04-22T20:52:13.620326Z 00O [0K  feature flags: FF_TIMESTAMPS:true[0;m
2025-04-22T20:52:13.620344Z 00O section_start:1745355133:prepare_executor
2025-04-22T20:52:13.620345Z 00O+[0K[0K[36;1mPreparing the "docker+machine" executor[0;m[0;m
2025-04-22T20:52:13.754912Z 00O [0KUsing Docker executor with image mcr.microsoft.com/dotnet/core/sdk:latest ...[0;m
2025-04-22T20:52:17.799637Z 00O [0KPulling docker image mcr.microsoft.com/dotnet/core/sdk:latest ...[0;m
2025-04-22T20:52:33.302549Z 00O [0KUsing docker image sha256:1e8401d05dea4bdf104418a6e99c3fbbef9db505b98d96188f67d54f493ba448 for mcr.microsoft.com/dotnet/core/sdk:latest with digest mcr.microsoft.com/dotnet/core/sdk@sha256:150d074697d1cda38a0c2185fe43895d84b5745841e9d15c5adba29604a6e4cb ...[0;m
2025-04-22T20:52:33.302612Z 00O section_end:1745355153:prepare_executor
2025-04-22T20:52:33.302614Z 00O+[0Ksection_start:1745355153:prepare_script
2025-04-22T20:52:33.302673Z 00O+[0K[0K[36;1mPreparing environment[0;m[0;m
2025-04-22T20:52:34.917947Z 01O Running on runner-zxwgkjap-project-40127262-concurrent-0 via runner-zxwgkjap-s-l-s-amd64-1745355028-fdc93daa...
2025-04-22T20:52:35.100568Z 00O section_end:1745355155:prepare_script
2025-04-22T20:52:35.100575Z 00O+[0Ksection_start:1745355155:get_sources
2025-04-22T20:52:35.100900Z 00O+[0K[0K[36;1mGetting source from Git repository[0;m[0;m
2025-04-22T20:52:35.477076Z 01O [32;1mFetching changes with git depth set to 20...[0;m
2025-04-22T20:52:35.489026Z 01O Initialized empty Git repository in /builds/X_Sheep/dotnet-test-ci/.git/
2025-04-22T20:52:35.492026Z 01O [32;1mCreated fresh repository.[0;m
2025-04-22T20:52:35.832849Z 01O [32;1mChecking out 6a35bae5 as detached HEAD (ref is some-branch)...[0;m
2025-04-22T20:52:35.876736Z 01O 
2025-04-22T20:52:35.876742Z 01O [32;1mSkipping Git submodules setup[0;m
2025-04-22T20:52:35.876743Z 01O [32;1m$ git remote set-url origin "${CI_REPOSITORY_URL}" || echo 'Not a git repository; skipping'[0;m
2025-04-22T20:52:36.647180Z 00O section_end:1745355156:get_sources
2025-04-22T20:52:36.647220Z 00O+[0Ksection_start:1745355156:download_artifacts
2025-04-22T20:52:36.651490Z 00O+[0K[0K[36;1mDownloading artifacts[0;m[0;m
2025-04-22T20:52:36.502301Z 01O [32;1mDownloading artifacts for build (9793131571)...[0;m
2025-04-22T20:52:36.952321Z 01E Downloading artifacts from coordinator... ok      [0;m  host[0;m=storage.googleapis.com id[0;m=9793131571 responseStatus[0;m=200 OK token[0;m=66_uxyTAS
2025-04-22T20:52:37.147633Z 00O section_end:1745355157:download_artifacts
2025-04-22T20:52:37.147639Z 00O+[0Ksection_start:1745355157:step_script
2025-04-22T20:52:37.147865Z 00O+[0K[0K[36;1mExecuting "step_script" stage of the job script[0;m[0;m
2025-04-22T20:52:37.148881Z 00O [0KUsing docker image sha256:1e8401d05dea4bdf104418a6e99c3fbbef9db505b98d96188f67d54f493ba448 for mcr.microsoft.com/dotnet/core/sdk:latest with digest mcr.microsoft.com/dotnet/core/sdk@sha256:150d074697d1cda38a0c2185fe43895d84b5745841e9d15c5adba29604a6e4cb ...[0;m
2025-04-22T20:52:37.565336Z 01O [32;1m$ dotnet run[0;m
2025-04-22T20:52:40.122468Z 01O [38;2;0;0;0m█[38;2;3;0;0m█[38;2;6;0;0m█[38;2;9;0;0m█[38;2;12;0;0m█[38;2;15;0;0m█[38;2;18;0;0m█[38;2;21;0;0m█[38;2;24;0;0m█[38;2;27;0;0m█[38;2;30;0;0m█[38;2;33;0;0m█[38;2;36;0;0m█[38;2;39;0;0m█[38;2;42;0;0m█[38;2;45;0;0m█[38;2;48;0;0m█[38;2;51;0;0m█[38;2;54;0;0m█[38;2;57;0;0m█[38;2;60;0;0m█[38;2;63;0;0m█[38;2;66;0;0m█[38;2;69;0;0m█[38;2;72;0;0m█[38;2;75;0;0m█[38;2;78;0;0m█[38;2;81;0;0m█[38;2;84;0;0m█[38;2;87;0;0m█[38;2;90;0;0m█[38;2;93;0;0m█[38;2;96;0;0m█[38;2;99;0;0m█[38;2;102;0;0m█[38;2;105;0;0m█[38;2;108;0;0m█[38;2;111;0;0m█[38;2;114;0;0m█[38;2;117;0;0m█[38;2;120;0;0m█[38;2;123;0;0m█[38;2;126;0;0m█[38;2;129;0;0m█[38;2;132;0;0m█[38;2;135;0;0m█[38;2;138;0;0m█[38;2;141;0;0m█[38;2;144;0;0m█[38;2;147;0;0m█[38;2;150;0;0m█[38;2;153;0;0m█[38;2;156;0;0m█[38;2;159;0;0m█[38;2;162;0;0m█[38;2;165;0;0m█[38;2;168;0;0m█[38;2;171;0;0m█[38;2;174;0;0m█[38;2;177;0;0m█[38;2;180;0;0m█[38;2;183;0;0m█[38;2;186;0;0m█[38;2;189;0;0m█[38;2;192;0;0m█[38;2;195;0;0m█[38;2;198;0;0m█[38;2;201;0;0m█[38;2;204;0;0m█[38;2;207;0;0m█[38;2;210;0;0m█[38;2;213;0;0m█[38;2;216;0;0m█[38;2;219;0;0m█[38;2;222;0;0m█[38;2;225;0;0m█[38;2;228;0;0m█[38;2;231;0;0m█[38;2;234;0;0m█[38;2;237;0;0m█[38;2;240;0;0m█[38;2;243;0;0m█[38;2;246;0;0m█[38;2;249;0;0m█[38;2;252;0;0m█[38;2;255;0;0m█
2025-04-22T20:52:40.123756Z 01O [38;2;0;0;0m█[38;2;0;3;0m█[38;2;0;6;0m█[38;2;0;9;0m█[38;2;0;12;0m█[38;2;0;15;0m█[38;2;0;18;0m█[38;2;0;21;0m█[38;2;0;24;0m█[38;2;0;27;0m█[38;2;0;30;0m█[38;2;0;33;0m█[38;2;0;36;0m█[38;2;0;39;0m█[38;2;0;42;0m█[38;2;0;45;0m█[38;2;0;48;0m█[38;2;0;51;0m█[38;2;0;54;0m█[38;2;0;57;0m█[38;2;0;60;0m█[38;2;0;63;0m█[38;2;0;66;0m█[38;2;0;69;0m█[38;2;0;72;0m█[38;2;0;75;0m█[38;2;0;78;0m█[38;2;0;81;0m█[38;2;0;84;0m█[38;2;0;87;0m█[38;2;0;90;0m█[38;2;0;93;0m█[38;2;0;96;0m█[38;2;0;99;0m█[38;2;0;102;0m█[38;2;0;105;0m█[38;2;0;108;0m█[38;2;0;111;0m█[38;2;0;114;0m█[38;2;0;117;0m█[38;2;0;120;0m█[38;2;0;123;0m█[38;2;0;126;0m█[38;2;0;129;0m█[38;2;0;132;0m█[38;2;0;135;0m█[38;2;0;138;0m█[38;2;0;141;0m█[38;2;0;144;0m█[38;2;0;147;0m█[38;2;0;150;0m█[38;2;0;153;0m█[38;2;0;156;0m█[38;2;0;159;0m█[38;2;0;162;0m█[38;2;0;165;0m█[38;2;0;168;0m█[38;2;0;171;0m█[38;2;0;174;0m█[38;2;0;177;0m█[38;2;0;180;0m█[38;2;0;183;0m█[38;2;0;186;0m█[38;2;0;189;0m█[38;2;0;192;0m█[38;2;0;195;0m█[38;2;0;198;0m█[38;2;0;201;0m█[38;2;0;204;0m█[38;2;0;207;0m█[38;2;0;210;0m█[38;2;0;213;0m█[38;2;0;216;0m█[38;2;0;219;0m█[38;2;0;222;0m█[38;2;0;225;0m█[38;2;0;228;0m█[38;2;0;231;0m█[38;2;0;234;0m█[38;2;0;237;0m█[38;2;0;240;0m█[38;2;0;243;0m█[38;2;0;246;0m█[38;2;0;249;0m█[38;2;0;252;0m█[38;2;0;255;0m█
2025-04-22T20:52:40.123765Z 01O [38;2;0;0;0m█[38;2;0;0;3m█[38;2;0;0;6m█[38;2;0;0;9m█[38;2;0;0;12m█[38;2;0;0;15m█[38;2;0;0;18m█[38;2;0;0;21m█[38;2;0;0;24m█[38;2;0;0;27m█[38;2;0;0;30m█[38;2;0;0;33m█[38;2;0;0;36m█[38;2;0;0;39m█[38;2;0;0;42m█[38;2;0;0;45m█[38;2;0;0;48m█[38;2;0;0;51m█[38;2;0;0;54m█[38;2;0;0;57m█[38;2;0;0;60m█[38;2;0;0;63m█[38;2;0;0;66m█[38;2;0;0;69m█[38;2;0;0;72m█[38;2;0;0;75m█[38;2;0;0;78m█[38;2;0;0;81m█[38;2;0;0;84m█[38;2;0;0;87m█[38;2;0;0;90m█[38;2;0;0;93m█[38;2;0;0;96m█[38;2;0;0;99m█[38;2;0;0;102m█[38;2;0;0;105m█[38;2;0;0;108m█[38;2;0;0;111m█[38;2;0;0;114m█[38;2;0;0;117m█[38;2;0;0;120m█[38;2;0;0;123m█[38;2;0;0;126m█[38;2;0;0;129m█[38;2;0;0;132m█[38;2;0;0;135m█[38;2;0;0;138m█[38;2;0;0;141m█[38;2;0;0;144m█[38;2;0;0;147m█[38;2;0;0;150m█[38;2;0;0;153m█[38;2;0;0;156m█[38;2;0;0;159m█[38;2;0;0;162m█[38;2;0;0;165m█[38;2;0;0;168m█[38;2;0;0;171m█[38;2;0;0;174m█[38;2;0;0;177m█[38;2;0;0;180m█[38;2;0;0;183m█[38;2;0;0;186m█[38;2;0;0;189m█[38;2;0;0;192m█[38;2;0;0;195m█[38;2;0;0;198m█[38;2;0;0;201m█[38;2;0;0;204m█[38;2;0;0;207m█[38;2;0;0;210m█[38;2;0;0;213m█[38;2;0;0;216m█[38;2;0;0;219m█[38;2;0;0;222m█[38;2;0;0;225m█[38;2;0;0;228m█[38;2;0;0;231m█[38;2;0;0;234m█[38;2;0;0;237m█[38;2;0;0;240m█[38;2;0;0;243m█[38;2;0;0;246m█[38;2;0;0;249m█[38;2;0;0;252m█[38;2;0;0;255m█
2025-04-22T20:52:40.123772Z 01O 
2025-04-22T20:52:40.123773Z 01O [30mblack[0m    [90mbright black[0m     [40mblack[0m    [100mbright black[0m
2025-04-22T20:52:40.123774Z 01O [31mred[0m      [91mbright red[0m       [41mred[0m      [101mbright red[0m
2025-04-22T20:52:40.123775Z 01O [32mgreen[0m    [92mbright green[0m     [42mgreen[0m    [102mbright green[0m
2025-04-22T20:52:40.123776Z 01O [33myellow[0m   [93mbright yellow[0m    [43myellow[0m   [103mbright yellow[0m
2025-04-22T20:52:40.123778Z 01O [34mblue[0m     [94mbright blue[0m      [44mblue[0m     [104mbright blue[0m
2025-04-22T20:52:40.123779Z 01O [35mmagenta[0m  [95mbright magenta[0m   [45mmagenta[0m  [105mbright magenta[0m
2025-04-22T20:52:40.123780Z 01O [36mcyan[0m     [96mbright cyan[0m      [46mcyan[0m     [106mbright cyan[0m
2025-04-22T20:52:40.123790Z 01O [37mwhite[0m    [97mbright white[0m     [47mwhite[0m    [107mbright white[0m
2025-04-22T20:52:40.123791Z 01O 
2025-04-22T20:52:40.123792Z 01O [1mbold[0m [2mdim[0m [3mitalic[3m [4munderline[4m
2025-04-22T20:52:40.123793Z 01O [m
2025-04-22T20:52:40.123793Z 01O Doing some task... 0%
2025-04-22T20:52:40.224468Z 01O+Doing some task... 4%
2025-04-22T20:52:40.324128Z 01O+Doing some task... 8%
2025-04-22T20:52:40.424877Z 01O+Doing some task... 12%
2025-04-22T20:52:40.525675Z 01O+Doing some task... 16%
2025-04-22T20:52:40.626149Z 01O+Doing some task... 20%
2025-04-22T20:52:40.725968Z 01O+Doing some task... 24%
2025-04-22T20:52:40.827053Z 01O+Doing some task... 28%
2025-04-22T20:52:40.926759Z 01O+Doing some task... 32%
2025-04-22T20:52:41.272000Z 01O+Doing some task... 36%
2025-04-22T20:52:41.127710Z 01O+Doing some task... 40%
2025-04-22T20:52:41.227964Z 01O+Doing some task... 44%
2025-04-22T20:52:41.328429Z 01O+Doing some task... 48%
2025-04-22T20:52:51.328830Z 01O+Doing some task... 52%
2025-04-22T20:52:51.429255Z 01O+Doing some task... 56%
2025-04-22T20:52:51.529707Z 01O+Doing some task... 60%
2025-04-22T20:52:51.630156Z 01O+Doing some task... 64%
2025-04-22T20:52:51.730664Z 01O+Doing some task... 68%
2025-04-22T20:52:51.831175Z 01O+Doing some task... 72%
2025-04-22T20:52:51.931653Z 01O+Doing some task... 76%
2025-04-22T20:52:52.321140Z 01O+Doing some task... 80%
2025-04-22T20:52:52.132662Z 01O+Doing some task... 84%
2025-04-22T20:52:52.233174Z 01O+Doing some task... 88%
2025-04-22T20:52:52.333575Z 01O+Doing some task... 92%
2025-04-22T20:52:52.434040Z 01O+Doing some task... 96%
2025-04-22T20:52:52.534413Z 01O+Doing some task... 100%
2025-04-22T20:52:52.634796Z 01O+
2025-04-22T20:52:52.634803Z 01O Task complete
2025-04-22T20:52:52.853768Z 00O section_end:1745355172:step_script
2025-04-22T20:52:52.853773Z 00O+[0Ksection_start:1745355172:cleanup_file_variables
2025-04-22T20:52:52.854211Z 00O+[0K[0K[36;1mCleaning up project directory and file based variables[0;m[0;m
2025-04-22T20:52:53.384109Z 00O section_end:1745355173:cleanup_file_variables
2025-04-22T20:52:53.384114Z 00O+[0K
2025-04-22T20:52:53.396446Z 00O [32;1mJob succeeded[0;m

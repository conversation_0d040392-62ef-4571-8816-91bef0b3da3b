import {
  Terminal,
  TerminalShellExecution,
  TerminalShellExecutionEndEvent,
  TerminalShellIntegration,
  TerminalShellIntegrationChangeEvent,
  window,
} from 'vscode';
import { BaseLanguageClient, GenericRequestHandler } from 'vscode-languageclient';
import { createFakePartial } from '../test_utils/create_fake_partial';
import { TerminalManager } from './terminal_manager';

type RunCommandResult = {
  output: string;
  exitCode: number | undefined;
};
type RunCommandEventHandler = GenericRequestHandler<RunCommandResult, Error>;

describe('Terminal Manager', () => {
  let mockClient: BaseLanguageClient;
  let terminalManager: TerminalManager;
  let mockShellIntegrationGetter: jest.Mock;
  let mockShellIntegration: TerminalShellIntegration;
  let mockExecution: TerminalShellExecution;
  let mockTerminal: Terminal;

  beforeEach(() => {
    mockClient = createFakePartial<BaseLanguageClient>({
      onRequest: jest.fn(),
    });
    mockShellIntegrationGetter = jest.fn();
    mockExecution = createFakePartial<TerminalShellExecution>({
      read: jest.fn(),
    });
    mockShellIntegration = createFakePartial<TerminalShellIntegration>({
      executeCommand: jest.fn(),
    });
    mockTerminal = createFakePartial<Terminal>({
      get shellIntegration() {
        return mockShellIntegrationGetter();
      },
      show: jest.fn(),
      dispose: jest.fn(),
    });

    jest.mocked(window.createTerminal).mockReturnValue(mockTerminal);

    terminalManager = new TerminalManager();
    jest.useFakeTimers();
  });

  describe('setup requests', () => {
    it('listens to `$/gitlab/runCommand` events', () => {
      terminalManager.setupRequests(mockClient);

      expect(mockClient.onRequest).toHaveBeenCalledWith(
        '$/gitlab/runCommand',
        expect.any(Function),
      );
    });

    it('listens to onDidCloseTerminal event', () => {
      terminalManager.setupRequests(mockClient);

      expect(window.onDidCloseTerminal).toHaveBeenCalledWith(expect.any(Function));
    });
  });

  describe('dispose', () => {
    let eventFn: RunCommandEventHandler;
    let shellExecutionFn: (e: TerminalShellExecutionEndEvent) => Disposable;
    let listenerDispose: jest.Mock;
    let closeTerminalDispose: jest.Mock;

    beforeEach(async () => {
      listenerDispose = jest.fn();
      closeTerminalDispose = jest.fn();
      jest.mocked(mockClient.onRequest).mockImplementation((event: string, fn) => {
        if (event === '$/gitlab/runCommand') {
          eventFn = fn as RunCommandEventHandler;
        }
        return { dispose: listenerDispose };
      });
      jest.mocked(window.onDidEndTerminalShellExecution).mockImplementation(fn => {
        shellExecutionFn = fn;
        return { dispose() {} };
      });
      jest.mocked(window.onDidCloseTerminal).mockImplementationOnce(() => {
        return { dispose: closeTerminalDispose };
      });
      jest.mocked(mockShellIntegration.executeCommand).mockReturnValue(mockExecution);

      terminalManager.setupRequests(mockClient);

      const workflowId = '1234';
      const command = 'npm';
      const args = ['run', 'test:unit'];
      mockShellIntegrationGetter.mockReturnValue(mockShellIntegration);

      jest.mocked(mockExecution.read).mockImplementation(async function* read() {
        yield '';
      });

      const result = eventFn({ workflowId, command, args });

      await jest.advanceTimersToNextTimerAsync();

      shellExecutionFn({
        execution: mockExecution,
        exitCode: 0,
        terminal: mockTerminal,
        shellIntegration: mockShellIntegration,
      });

      return result;
    });

    it('disposes all terminals and request lisener', async () => {
      terminalManager.dispose();

      expect(listenerDispose).toHaveBeenCalled();
      expect(closeTerminalDispose).toHaveBeenCalled();
      expect(mockTerminal.dispose).toHaveBeenCalled();
    });
  });

  describe('$/gitlab/runCommand', () => {
    const workflowId = '1234';
    const command = 'npm';
    const args = ['run', 'test:unit'];
    let eventFn: RunCommandEventHandler;
    let shellIntegrationFn: (
      e: TerminalShellIntegrationChangeEvent,
      // thisArgs?: any,
      // disposables?: Disposable[],
    ) => Disposable;
    let shellExecutionFn: (e: TerminalShellExecutionEndEvent) => Disposable;
    let onDidCloseTerminalListener: (terminal: Terminal) => void;
    let shellExecDispose: jest.Mock;
    let shellIntDispose: jest.Mock;

    beforeEach(() => {
      shellExecDispose = jest.fn();
      shellIntDispose = jest.fn();
      onDidCloseTerminalListener = jest.fn();

      jest.mocked(mockClient.onRequest).mockImplementation((event: string, fn) => {
        if (event === '$/gitlab/runCommand') {
          eventFn = fn as RunCommandEventHandler;
        }
        return { dispose() {} };
      });
      jest.mocked(window.onDidChangeTerminalShellIntegration).mockImplementation(fn => {
        shellIntegrationFn = fn;
        return { dispose: shellIntDispose };
      });
      jest.mocked(window.onDidEndTerminalShellExecution).mockImplementation(fn => {
        shellExecutionFn = fn;
        return { dispose: shellExecDispose };
      });
      jest.mocked(mockShellIntegration.executeCommand).mockReturnValue(mockExecution);
      jest.mocked(window.onDidCloseTerminal).mockImplementationOnce(listener => {
        onDidCloseTerminalListener = listener;

        return { dispose() {} };
      });

      terminalManager.setupRequests(mockClient);
    });

    it('executes a command when the shell integration is available', async () => {
      const processOutput = ['running tests...\n', 'all tests passed!'];
      mockShellIntegrationGetter.mockReturnValue(undefined);

      jest.mocked(mockExecution.read).mockImplementation(async function* read() {
        for (const line of processOutput) {
          yield line;
        }
      });

      const result = eventFn({ workflowId, command, args });

      shellIntegrationFn({ terminal: mockTerminal, shellIntegration: mockShellIntegration });

      mockShellIntegrationGetter.mockReturnValue(mockShellIntegration);

      await jest.advanceTimersToNextTimerAsync();

      shellExecutionFn({
        execution: mockExecution,
        exitCode: 0,
        terminal: mockTerminal,
        shellIntegration: mockShellIntegration,
      });

      expect(window.createTerminal).toHaveBeenCalledWith({
        name: `GitLab Duo Agent Platform ${workflowId}`,
        isTransient: true,
      });

      expect(window.onDidChangeTerminalShellIntegration).toHaveBeenCalled();

      expect(mockTerminal.show).toHaveBeenCalled();
      expect(mockShellIntegration.executeCommand).toHaveBeenCalledWith(command, args);
      expect(mockExecution.read).toHaveBeenCalled();
      expect(window.onDidEndTerminalShellExecution).toHaveBeenCalled();
      expect(shellExecDispose).toHaveBeenCalled();
      expect(shellIntDispose).toHaveBeenCalled();

      const { output, exitCode } = (await result) as unknown as RunCommandResult;
      expect(output).toBe(processOutput.join(''));
      expect(exitCode).toBe(0);
    });

    it('reuses a terminal when the same workflow ID is used', async () => {
      mockShellIntegrationGetter.mockReturnValue(mockShellIntegration);

      jest.mocked(mockExecution.read).mockImplementation(async function* read() {
        yield '';
      });

      const firstResult = eventFn({ workflowId, command, args });

      await jest.advanceTimersToNextTimerAsync();

      shellExecutionFn({
        execution: mockExecution,
        exitCode: 0,
        terminal: mockTerminal,
        shellIntegration: mockShellIntegration,
      });

      await firstResult;

      const secondResult = eventFn({ workflowId, command, args });

      await jest.advanceTimersToNextTimerAsync();

      shellExecutionFn({
        execution: mockExecution,
        exitCode: 0,
        terminal: mockTerminal,
        shellIntegration: mockShellIntegration,
      });

      await secondResult;

      expect(window.createTerminal).toHaveBeenCalledTimes(1);
    });

    it('re-creates terminal if the terminal is closed', async () => {
      mockShellIntegrationGetter.mockReturnValue(mockShellIntegration);

      jest.mocked(mockExecution.read).mockImplementation(async function* read() {
        yield '';
      });

      let result = eventFn({ workflowId, command, args });

      await jest.advanceTimersToNextTimerAsync();

      shellExecutionFn({
        execution: mockExecution,
        exitCode: 0,
        terminal: mockTerminal,
        shellIntegration: mockShellIntegration,
      });

      await result;

      expect(window.createTerminal).toHaveBeenCalledTimes(1);

      onDidCloseTerminalListener(mockTerminal);

      result = eventFn({ workflowId, command, args });

      await jest.advanceTimersToNextTimerAsync();

      shellExecutionFn({
        execution: mockExecution,
        exitCode: 0,
        terminal: mockTerminal,
        shellIntegration: mockShellIntegration,
      });

      await jest.advanceTimersToNextTimerAsync();

      expect(window.createTerminal).toHaveBeenCalledTimes(2);
    });

    it('throws when shell integration is not available', async () => {
      mockShellIntegrationGetter.mockReturnValue(undefined);

      const result = eventFn({ workflowId, command, args });

      jest.advanceTimersToNextTimer();

      await expect(result).rejects.toThrow(
        new Error('User does not have shell integration configured'),
      );
    });
  });
});

import { Terminal, window, Disposable } from 'vscode';
import { BaseLanguageClient } from 'vscode-languageclient';
import { log } from '../log';

export class TerminalManager implements Disposable {
  #terminals: Map<string, Terminal>;

  #disposables: Disposable[] = [];

  constructor() {
    this.#terminals = new Map();
  }

  setupRequests(client: BaseLanguageClient) {
    this.#disposables.push(
      client.onRequest('$/gitlab/runCommand', async ({ workflowId, command, args }) => {
        log.debug(`Running command: ${command} ${args.join(' ')}`);

        return this.#executeCommand(workflowId, command, args);
      }),
    );

    this.#disposables.push(
      window.onDidCloseTerminal(closedTerminal => {
        for (const [workflowId, terminal] of this.#terminals.entries()) {
          if (terminal === closedTerminal) {
            this.#terminals.delete(workflowId);
            break;
          }
        }
      }),
    );
  }

  async #executeCommand(workflowId: string, command: string, args: string[]) {
    const terminal = await this.#getOrCreateTerminal(workflowId);

    if (!terminal.shellIntegration) {
      throw new Error('User does not have shell integration configured');
    }

    terminal.show(true);

    const exec = terminal.shellIntegration.executeCommand(command, args);
    const executionEnd = new Promise(resolve => {
      const { dispose } = window.onDidEndTerminalShellExecution(({ execution, exitCode }) => {
        if (exec === execution) {
          dispose();
          resolve(exitCode);
        }
      });
    });

    const output: string[] = [];
    const stream = exec.read();
    for await (const data of stream) {
      output.push(data);
    }

    const exitCode = await executionEnd;

    return { exitCode, output: output.join('') };
  }

  async #getOrCreateTerminal(workflowId: string) {
    return this.#terminals.get(workflowId) ?? this.#createTerminal(workflowId);
  }

  async #createTerminal(workflowId: string) {
    const terminal = window.createTerminal({
      name: `GitLab Duo Agent Platform ${workflowId}`,
      isTransient: true,
    });

    this.#terminals.set(workflowId, terminal);

    await this.#listenForShellIntegration(terminal);

    return terminal;
  }

  #listenForShellIntegration(term: Terminal) {
    return new Promise((resolve, reject) => {
      if (term.shellIntegration) {
        resolve(term);
        return;
      }
      const { dispose } = window.onDidChangeTerminalShellIntegration(
        ({ terminal, shellIntegration }) => {
          if (term === terminal && shellIntegration) {
            dispose();
            resolve(terminal);
          }
        },
      );

      setTimeout(() => {
        reject(new Error('User does not have shell integration configured'));
      }, 3000);
    });
  }

  dispose() {
    this.#terminals.forEach(term => {
      term.dispose();
    });

    this.#disposables.forEach(disposable => {
      disposable.dispose();
    });
  }
}

import { commonJobs, generateAssets, writePackageJson } from './utils/common_jobs.mjs';
import { createBrowserPackageJson } from './utils/packages.mjs';
import { ENVIRONMENTS } from './constants.mjs';
import { watchBrowser, watchWebviews } from './utils/browser_jobs.mjs';

async function main() {
  const packageJson = createBrowserPackageJson();

  await commonJobs(ENVIRONMENTS.BROWSER);

  await Promise.all([
    writePackageJson(packageJson, ENVIRONMENTS.BROWSER),
    generateAssets(packageJson, ENVIRONMENTS.BROWSER),
  ]);
  const abortController = new AbortController();
  process.on('exit', () => abortController.abort());
  ['SIGINT', 'SIGQUIT', 'SIGHUP'].forEach(signal => {
    process.on(signal, () => abortController.abort());
  });

  watchBrowser(abortController.signal);
  watchWebviews(abortController.signal);
}

main();

import path from 'node:path';
import fs from 'node:fs';
import { ENVIRONMENTS } from '../constants.mjs';
import { root, run } from './run_utils.mjs';
import { createBrowserPackageJson } from './packages.mjs';
import {
  prepareWebviews,
  generateAssets,
  writePackageJson,
  commonJobs,
  copyStaticProjectFiles,
} from './common_jobs.mjs';

const browserWebviews = {
  vue3: [],
  vue2: ['gitlab_duo_chat', 'security_finding'],
};

export function watchWebviews(signal) {
  const targets = Object.keys(browserWebviews);
  targets.forEach(async target => {
    browserWebviews[target].forEach(webview => {
      const dirpath = path.join(root, `webviews/${target}/dist/${webview}`);
      if (!fs.existsSync(dirpath)) fs.mkdirSync(dirpath, { recursive: true });
      fs.symlinkSync(dirpath, path.join(root, `dist-browser/webviews/${webview}`));
    });

    if (browserWebviews[target].length) {
      await run('npm', ['run', '--prefix', path.join(root, `webviews/${target}`), 'watch'], {
        cancelSignal: signal,
      });
    }
  });
}

function typecheck(signal) {
  return run('tsc', ['-p', root, '--noEmit'], { cancelSignal: signal });
}

/**
 * Build the extension for browser environment
 * @param {string[]} [args=[]] - Build arguments
 * @param {AbortSignal} [signal] - Optional abort signal to cancel the operation
 * @returns {Promise<void>}
 */
async function buildExtension(args = [], signal) {
  await typecheck(signal);

  await run(
    'esbuild',
    [
      path.join(root, 'src/browser/browser.js'),
      '--bundle',
      '--outfile=dist-browser/browser.js',
      '--external:vscode',
      // For the fs fix, see:
      // https://github.com/tree-sitter/tree-sitter/tree/660481dbf71413eba5a928b0b0ab8da50c1109e0/lib/binding_web#cant-resolve-fs-in-node_modulesweb-tree-sitter
      '--external:fs',
      // `graceful-fs` is a dependency of `enhanced-resolve`, used in the LS. The dependency is not needed, as the LS supplies our own fsClient, so we prevent it from being included in the bundle
      '--external:graceful-fs',
      '--format=cjs',
      '--sourcemap',
      '--source-root=../gitlab-vscode-extension/src',
      '--alias:path=path-browserify',
      '--loader:.html=text',
      ...args,
    ],
    { cancelSignal: signal },
  );
}

export async function buildBrowser() {
  const packageJson = createBrowserPackageJson();

  await commonJobs(ENVIRONMENTS.BROWSER);

  await Promise.all([
    prepareWebviews(browserWebviews, ENVIRONMENTS.BROWSER),
    writePackageJson(packageJson, ENVIRONMENTS.BROWSER),
    buildExtension(process.env.CI ? ['--minify'] : []),
    generateAssets(packageJson, ENVIRONMENTS.BROWSER),
  ]);
}

export async function watchBrowser(signal) {
  const packageJson = createBrowserPackageJson();
  await writePackageJson(packageJson, ENVIRONMENTS.BROWSER);
  copyStaticProjectFiles(ENVIRONMENTS.BROWSER);

  await buildExtension(['--watch'], signal);
}

// eslint-disable-next-line import/no-default-export
export default {};

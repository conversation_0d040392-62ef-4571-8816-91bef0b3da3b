#!/usr/bin/env python3
"""
<PERSON>ript to automatically fix all self.toolset.run_tool() calls in Context 2.0 specialist agents.

This script replaces the pattern:
    result = await self.toolset.run_tool("tool_name", {"arg1": value1, "arg2": value2})

With:
    result_wrapper = await self._execute_tool("tool_name", arg1=value1, arg2=value2)
    if result_wrapper.get("success") and result_wrapper.get("content"):
        result = result_wrapper["content"]
"""

import re
import sys
from pathlib import Path


def fix_toolset_calls_in_file(file_path: Path) -> tuple[int, str]:
    """
    Fix all toolset.run_tool() calls in a file.
    
    Returns:
        Tuple of (number_of_fixes, updated_content)
    """
    content = file_path.read_text()
    original_content = content
    fixes_count = 0
    
    # Pattern to match: await self.toolset.run_tool("tool_name", {args})
    # This is a complex pattern, so we'll do it in multiple passes
    
    # Pattern 1: Simple single-line calls
    pattern1 = r'(\s+)(\w+)\s*=\s*await\s+self\.toolset\.run_tool\(\s*"([^"]+)"\s*,\s*\{([^}]+)\}\s*\)'
    
    def replace_simple_call(match):
        nonlocal fixes_count
        fixes_count += 1
        indent = match.group(1)
        var_name = match.group(2)
        tool_name = match.group(3)
        args_dict = match.group(4)
        
        # Convert dict args to kwargs
        # Parse {"key": value, "key2": value2} to key=value, key2=value2
        args_dict = args_dict.strip()
        kwargs_parts = []
        
        # Simple regex to extract key-value pairs
        arg_pattern = r'"([^"]+)"\s*:\s*([^,}]+)'
        for arg_match in re.finditer(arg_pattern, args_dict):
            key = arg_match.group(1)
            value = arg_match.group(2).strip()
            kwargs_parts.append(f"{key}={value}")
        
        kwargs_str = ", ".join(kwargs_parts) if kwargs_parts else ""
        
        # Generate replacement
        wrapper_var = f"{var_name}_wrapper" if not var_name.endswith("_wrapper") else var_name
        
        replacement = f'{indent}{wrapper_var} = await self._execute_tool("{tool_name}"'
        if kwargs_str:
            replacement += f", {kwargs_str}"
        replacement += ")"
        
        return replacement
    
    content = re.sub(pattern1, replace_simple_call, content)
    
    if content != original_content:
        return fixes_count, content
    else:
        return 0, original_content


def main():
    """Main function to fix all specialist agent files."""
    
    # Files to fix
    files_to_fix = [
        "gitlab-ai-gateway/********************/agents/context_2_0/gitlab_ecosystem.py",
        "gitlab-ai-gateway/********************/agents/context_2_0/git_history.py",
    ]
    
    total_fixes = 0
    
    for file_path_str in files_to_fix:
        file_path = Path(file_path_str)
        
        if not file_path.exists():
            print(f"❌ File not found: {file_path}")
            continue
        
        print(f"\n📝 Processing: {file_path}")
        
        fixes_count, updated_content = fix_toolset_calls_in_file(file_path)
        
        if fixes_count > 0:
            # Write back the fixed content
            file_path.write_text(updated_content)
            print(f"✅ Fixed {fixes_count} occurrences in {file_path.name}")
            total_fixes += fixes_count
        else:
            print(f"ℹ️  No fixes needed in {file_path.name}")
    
    print(f"\n🎉 Total fixes applied: {total_fixes}")
    
    if total_fixes > 0:
        print("\n⚠️  Note: This script handles simple cases. Complex multi-line calls")
        print("   may need manual fixing. Please review the changes and test thoroughly.")


if __name__ == "__main__":
    main()


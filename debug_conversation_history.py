#!/usr/bin/env python3
"""
Debug script to understand why the conversation history is not being preserved
in the Context 2.0 workflow, causing infinite loops.
"""

import sys
import os

# Add the gitlab-ai-gateway directory to the path
sys.path.insert(0, '/Users/<USER>/Developer/gitlab/gdk/gitlab-ai-gateway')

def ********************():
    """Debug the workflow logic to understand the conversation history flow."""
    try:
        # Read the context_2_workflow file
        workflow_file = '/Users/<USER>/Developer/gitlab/gdk/gitlab-ai-gateway/********************/agents/context_2_0/context_2_workflow.py'
        
        with open(workflow_file, 'r') as f:
            content = f.read()
        
        print("🔍 Debugging workflow conversation history logic...")
        
        # Check the continuation condition
        continuation_condition = 'if agent_key in conversation_history and len(conversation_history[agent_key]) > 1:'
        if continuation_condition in content:
            print("✅ Continuation condition found: len(conversation_history[agent_key]) > 1")
        else:
            print("❌ Continuation condition NOT found or different")
            return False
        
        # Check if tool results are extracted
        if 'for msg in messages:' in content and 'msg.type == \'tool\'' in content:
            print("✅ Tool results extraction logic found")
        else:
            print("❌ Tool results extraction logic NOT found")
            return False
        
        # Check if investigate is called with tool_results
        if 'result = await agent.investigate(goal, context, tool_results=tool_results)' in content:
            print("✅ investigate called with tool_results for continuation")
        else:
            print("❌ investigate NOT called with tool_results")
            return False
        
        # Check first call logic
        if 'ai_message = await agent.investigate(goal, context, use_tools_executor=True)' in content:
            print("✅ First call uses use_tools_executor=True")
        else:
            print("❌ First call logic NOT found")
            return False
        
        print("✅ Workflow logic appears correct")
        return True
        
    except Exception as e:
        print(f"❌ Error debugging workflow: {e}")
        return False

def debug_agent_investigate_method():
    """Debug the agent investigate method to see the execution paths."""
    try:
        # Read the base_specialist_agent file
        agent_file = '/Users/<USER>/Developer/gitlab/gdk/gitlab-ai-gateway/********************/agents/context_2_0/base_specialist_agent.py'
        
        with open(agent_file, 'r') as f:
            content = f.read()
        
        print("\n🔍 Debugging agent investigate method...")
        
        # Check the investigate method signature
        if 'async def investigate(' in content and 'tool_results: Optional[List[Dict[str, Any]]] = None' in content:
            print("✅ investigate method has tool_results parameter")
        else:
            print("❌ investigate method missing tool_results parameter")
            return False
        
        # Check the tool_results handling
        if 'if tool_results is not None:' in content:
            print("✅ investigate method checks for tool_results")
        else:
            print("❌ investigate method does NOT check for tool_results")
            return False
        
        # Check if it calls the new decision method
        if 'return await self.process_tool_results_and_decide(query, context, tool_results)' in content:
            print("✅ investigate calls process_tool_results_and_decide for tool_results")
        else:
            print("❌ investigate does NOT call process_tool_results_and_decide")
            return False
        
        # Check use_tools_executor handling
        if 'if use_tools_executor and self.prompt_registry and self.user:' in content:
            print("✅ investigate handles use_tools_executor flag")
        else:
            print("❌ investigate does NOT handle use_tools_executor flag")
            return False
        
        # Check if it calls get_tool_calls_for_investigation
        if 'return await self.get_tool_calls_for_investigation(query, context)' in content:
            print("✅ investigate calls get_tool_calls_for_investigation for first call")
        else:
            print("❌ investigate does NOT call get_tool_calls_for_investigation")
            return False
        
        print("✅ Agent investigate method appears correct")
        return True
        
    except Exception as e:
        print(f"❌ Error debugging agent: {e}")
        return False

def debug_conversation_history_state():
    """Debug the conversation history state definition."""
    try:
        # Read the enhanced_state file
        state_file = '/Users/<USER>/Developer/gitlab/gdk/gitlab-ai-gateway/********************/agents/context_2_0/enhanced_state.py'
        
        with open(state_file, 'r') as f:
            content = f.read()
        
        print("\n🔍 Debugging conversation history state definition...")
        
        # Check if the reducer is properly imported and used
        if '_conversation_history_reducer' in content and 'Annotated[' in content:
            print("✅ Conversation history reducer properly imported and used")
        else:
            print("❌ Conversation history reducer NOT properly imported/used")
            return False
        
        # Check the exact type definition
        if 'Dict[str, List[BaseMessage]], _conversation_history_reducer' in content:
            print("✅ Conversation history has correct type with reducer")
        else:
            print("❌ Conversation history type definition incorrect")
            return False
        
        print("✅ Conversation history state definition appears correct")
        return True
        
    except Exception as e:
        print(f"❌ Error debugging state: {e}")
        return False

def analyze_potential_issues():
    """Analyze potential issues that could cause the infinite loop."""
    print("\n🤔 Analyzing potential issues...")
    
    issues = [
        "1. Conversation history not being preserved between LangGraph node executions",
        "2. Agent key mismatch between workflow and conversation history",
        "3. ToolMessage objects not being created properly by ToolsExecutor",
        "4. LangGraph state merging not working as expected",
        "5. Conversation history reducer not being called",
        "6. Agent investigate method not receiving conversation history",
        "7. First call vs continuation logic not working properly"
    ]
    
    print("Potential root causes:")
    for issue in issues:
        print(f"   • {issue}")
    
    print("\n💡 Debugging suggestions:")
    print("   1. Add logging to _create_specialist_agent_node to see conversation_history content")
    print("   2. Add logging to investigate method to see which path is taken")
    print("   3. Add logging to conversation history reducer to see if it's called")
    print("   4. Check if agent_key matches between workflow and conversation history")
    print("   5. Verify ToolMessage objects are created with correct attributes")

def main():
    """Run debugging analysis."""
    print("🐛 Debugging Context 2.0 Infinite Loop Issue")
    print("=" * 50)
    
    tests = [
        ("Workflow Logic", ********************),
        ("Agent Investigate Method", debug_agent_investigate_method),
        ("Conversation History State", debug_conversation_history_state),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Debugging: {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name}: OK")
        else:
            print(f"❌ {test_name}: ISSUES FOUND")
    
    analyze_potential_issues()
    
    print("\n" + "=" * 50)
    print(f"📊 Debug Results: {passed}/{total} components appear correct")
    
    if passed == total:
        print("\n🤔 All components appear correct, but infinite loop persists.")
        print("The issue is likely in the runtime execution flow or state management.")
        print("\n🔧 Next steps:")
        print("   1. Add runtime logging to see actual conversation history content")
        print("   2. Check if continuation condition is actually being met")
        print("   3. Verify that tool results are being extracted properly")
        print("   4. Ensure LangGraph state merging is working correctly")
    else:
        print("\n⚠️  Found issues in the implementation that need to be fixed.")

if __name__ == "__main__":
    main()

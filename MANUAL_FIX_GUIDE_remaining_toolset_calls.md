# Manual Fix Guide for Remaining toolset.run_tool() Calls

## Quick Reference

### Pattern to Find
```python
result = await self.toolset.run_tool("tool_name", {"arg1": value1, "arg2": value2})
```

### Pattern to Replace With
```python
result_wrapper = await self._execute_tool("tool_name", arg1=value1, arg2=value2)
if result_wrapper.get("success") and result_wrapper.get("content"):
    result = result_wrapper["content"]
```

## Remaining Files to Fix

### 1. gitlab_ecosystem.py (8 occurrences)

#### Fix 1: Line 271 - gitlab_issue_search
**Find:**
```python
search_results = await self.toolset.run_tool("gitlab_issue_search", {
    "query": keyword,
    "max_results": 10
})

if search_results and search_results.get("issues"):
```

**Replace with:**
```python
search_results_wrapper = await self._execute_tool("gitlab_issue_search",
    query=keyword,
    max_results=10
)

if search_results_wrapper.get("success") and search_results_wrapper.get("content"):
    search_results = search_results_wrapper["content"]
    if search_results and search_results.get("issues"):
```

#### Fix 2: Line 282 - list_issues
**Find:**
```python
recent_issues = await self.toolset.run_tool("list_issues", {
    "state": "all",
    "sort": "updated_desc",
    "per_page": 20
})

if recent_issues and recent_issues.get("issues"):
```

**Replace with:**
```python
recent_issues_wrapper = await self._execute_tool("list_issues",
    state="all",
    sort="updated_desc",
    per_page=20
)

if recent_issues_wrapper.get("success") and recent_issues_wrapper.get("content"):
    recent_issues = recent_issues_wrapper["content"]
    if recent_issues and recent_issues.get("issues"):
```

#### Fix 3: Line 303 - get_issue
**Find:**
```python
detailed_issue = await self.toolset.run_tool("get_issue", {"issue_id": issue_id})
if detailed_issue:
    issue["detailed_info"] = detailed_issue
```

**Replace with:**
```python
detailed_issue_wrapper = await self._execute_tool("get_issue", issue_id=issue_id)
if detailed_issue_wrapper.get("success") and detailed_issue_wrapper.get("content"):
    detailed_issue = detailed_issue_wrapper["content"]
    if detailed_issue:
        issue["detailed_info"] = detailed_issue
```

#### Fix 4: Line 308 - list_issue_notes
**Find:**
```python
issue_notes = await self.toolset.run_tool("list_issue_notes", {"issue_id": issue_id})
if issue_notes:
    issue["notes"] = issue_notes.get("notes", [])
```

**Replace with:**
```python
issue_notes_wrapper = await self._execute_tool("list_issue_notes", issue_id=issue_id)
if issue_notes_wrapper.get("success") and issue_notes_wrapper.get("content"):
    issue_notes = issue_notes_wrapper["content"]
    if issue_notes:
        issue["notes"] = issue_notes.get("notes", [])
```

#### Fix 5: Line 411 - gitlab_merge_request_search
**Find:**
```python
search_results = await self.toolset.run_tool("gitlab_merge_request_search", {
    "query": keyword,
    "max_results": 8
})

if search_results and search_results.get("merge_requests"):
```

**Replace with:**
```python
search_results_wrapper = await self._execute_tool("gitlab_merge_request_search",
    query=keyword,
    max_results=8
)

if search_results_wrapper.get("success") and search_results_wrapper.get("content"):
    search_results = search_results_wrapper["content"]
    if search_results and search_results.get("merge_requests"):
```

#### Fix 6: Line 425 - get_merge_request
**Find:**
```python
detailed_mr = await self.toolset.run_tool("get_merge_request", {"merge_request_id": mr_id})
if detailed_mr:
    mr["detailed_info"] = detailed_mr
```

**Replace with:**
```python
detailed_mr_wrapper = await self._execute_tool("get_merge_request", merge_request_id=mr_id)
if detailed_mr_wrapper.get("success") and detailed_mr_wrapper.get("content"):
    detailed_mr = detailed_mr_wrapper["content"]
    if detailed_mr:
        mr["detailed_info"] = detailed_mr
```

#### Fix 7: Line 518 - list_epics
**Find:**
```python
epics = await self.toolset.run_tool("list_epics", {"max_results": 10})

if epics and epics.get("epics"):
```

**Replace with:**
```python
epics_wrapper = await self._execute_tool("list_epics", max_results=10)

if epics_wrapper.get("success") and epics_wrapper.get("content"):
    epics = epics_wrapper["content"]
    if epics and epics.get("epics"):
```

#### Fix 8: Line 563 - get_previous_session_context
**Find:**
```python
previous_context = await self.toolset.run_tool("get_previous_session_context", {})

if previous_context:
```

**Replace with:**
```python
previous_context_wrapper = await self._execute_tool("get_previous_session_context")

if previous_context_wrapper.get("success") and previous_context_wrapper.get("content"):
    previous_context = previous_context_wrapper["content"]
    if previous_context:
```

### 2. git_history.py (6 occurrences)

#### Fix 1: Line 182 - list_commits
**Find:**
```python
recent_commits = await self.toolset.run_tool("list_commits", {
    "per_page": 50,
    "since": (datetime.now() - timedelta(days=30)).isoformat()
})

if recent_commits and recent_commits.get("commits"):
```

**Replace with:**
```python
recent_commits_wrapper = await self._execute_tool("list_commits",
    per_page=50,
    since=(datetime.now() - timedelta(days=30)).isoformat()
)

if recent_commits_wrapper.get("success") and recent_commits_wrapper.get("content"):
    recent_commits = recent_commits_wrapper["content"]
    if recent_commits and recent_commits.get("commits"):
```

#### Fix 2: Line 204 - get_commit
**Find:**
```python
detailed_commit = await self.toolset.run_tool("get_commit", {
    "commit_sha": commit_sha
})
if detailed_commit:
    commit["detailed_info"] = detailed_commit
```

**Replace with:**
```python
detailed_commit_wrapper = await self._execute_tool("get_commit", commit_sha=commit_sha)
if detailed_commit_wrapper.get("success") and detailed_commit_wrapper.get("content"):
    detailed_commit = detailed_commit_wrapper["content"]
    if detailed_commit:
        commit["detailed_info"] = detailed_commit
```

#### Fix 3: Line 398 - run_read_only_git_command
**Find:**
```python
git_search = await self.toolset.run_tool("run_read_only_git_command", {
    "command": f"log --grep='{keyword}' --oneline -n 10"
})

if git_search and git_search.get("output"):
```

**Replace with:**
```python
git_search_wrapper = await self._execute_tool("run_read_only_git_command",
    command=f"log --grep='{keyword}' --oneline -n 10"
)

if git_search_wrapper.get("success") and git_search_wrapper.get("content"):
    git_search = git_search_wrapper["content"]
    if git_search and git_search.get("output"):
```

#### Fix 4: Line 413 - get_commit (second occurrence)
Same pattern as Fix 2 above.

#### Fix 5: Line 430 - get_commit_diff
**Find:**
```python
commit_diff = await self.toolset.run_tool("get_commit_diff", {
    "commit_sha": commit_hash
})

if commit_diff:
```

**Replace with:**
```python
commit_diff_wrapper = await self._execute_tool("get_commit_diff", commit_sha=commit_hash)

if commit_diff_wrapper.get("success") and commit_diff_wrapper.get("content"):
    commit_diff = commit_diff_wrapper["content"]
    if commit_diff:
```

#### Fix 6: Line 572 - run_read_only_git_command (second occurrence)
**Find:**
```python
branch_info = await self.toolset.run_tool("run_read_only_git_command", {
    "command": "branch -a"
})

if branch_info and branch_info.get("output"):
```

**Replace with:**
```python
branch_info_wrapper = await self._execute_tool("run_read_only_git_command", command="branch -a")

if branch_info_wrapper.get("success") and branch_info_wrapper.get("content"):
    branch_info = branch_info_wrapper["content"]
    if branch_info and branch_info.get("output"):
```

## Testing After Fixes

1. Run Python syntax check:
   ```bash
   python3 -m py_compile gitlab-ai-gateway/********************/agents/context_2_0/*.py
   ```

2. Test Context 2.0 workflow in local GitLab instance

3. Check LangSmith traces for proper execution

4. Verify no more "Toolset object has no attribute 'run_tool'" errors


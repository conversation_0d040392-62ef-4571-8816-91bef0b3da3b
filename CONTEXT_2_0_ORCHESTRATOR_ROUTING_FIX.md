# Context 2.0 Orchestrator Routing Fix

## Problem

The orchestrator was generating complete analysis responses instead of routing to specialist agents. When a user asked "how is the prompt registry working?", the orchestrator produced a full 1,948 token analysis instead of routing to specialists.

**Observed Behavior**:
```
Context_2_0_Orchestrator: 37.84s, 3,652 tokens
Output: "I'll analyze your goal and orchestrate the appropriate specialist agents...
[Full analysis of prompt registry with detailed explanations]"
```

**Expected Behavior**:
- Orchestrator should output a routing decision
- Specialist agents should perform the actual analysis
- Multiple agents should be invoked sequentially

## Root Cause

The orchestrator prompt (`context_2_0_orchestrator/system/1.0.0.jinja`) was misleading:

1. **Told orchestrator to "invoke agents as tools"** - But orchestrator has NO tools
2. **Provided detailed analysis guidelines** - Made orchestrator think it should analyze
3. **No clear output format** - Orchestrator didn't know to output routing decisions only
4. **Router looks for keywords** - Router function parses content for agent names like "repository explorer"

The orchestrator interpreted its role as "analyze and explain" rather than "route to specialists".

## Solution

Completely rewrote the orchestrator prompt to emphasize routing-only behavior:

### Key Changes

1. **Clear Role Definition** (lines 1-10):
   ```
   You are a **ROUTER**, not an analyst. Your job is to:
   1. Analyze the user's goal
   2. Decide which specialist agent should investigate next
   3. Output ONLY the routing decision in your response
   
   **CRITICAL**: You do NOT perform analysis yourself.
   ```

2. **Mandatory Output Format** (lines 105-140):
   ```
   ## Routing Decision
   
   **Next Agent**: [Agent Name]
   
   **Reasoning**: [Brief 1-2 sentence explanation]
   
   **Focus Areas**: [What the agent should investigate]
   ```

3. **Valid Agent Names** (lines 119-126):
   - Repository Explorer
   - Code Navigator
   - Git History
   - GitLab Ecosystem
   - Context Synthesizer

4. **Concrete Examples** (lines 161-196):
   - Shows exact format for routing decisions
   - Demonstrates sequential routing
   - Shows how to route after specialist findings

5. **Updated Guidelines** (lines 142-149):
   - "You are a ROUTER, not an analyst"
   - "Output only routing decisions, never perform analysis"
   - "Use exact agent names"
   - "Be concise: 1-2 sentences maximum"

### What Was Removed

- ❌ "Invoke agents as tools" instructions
- ❌ Detailed analysis guidelines
- ❌ Tool usage examples
- ❌ Handover tool instructions (orchestrator doesn't use handover)

### What Was Added

- ✅ Explicit "ROUTER, not analyst" role
- ✅ Mandatory output format
- ✅ Exact agent names to use
- ✅ Concrete routing decision examples
- ✅ "DO NOT provide analysis" warning

## Files Modified

### `gitlab-ai-gateway/ai_gateway/prompts/definitions/workflow/context_2_0_orchestrator/system/1.0.0.jinja`

**Changes**:
1. Lines 1-10: Rewrote role definition to emphasize routing
2. Lines 105-140: Added mandatory output format section
3. Lines 142-149: Updated guidelines to prevent analysis
4. Lines 161-196: Added concrete routing decision examples
5. Removed: Tool invocation instructions, handover instructions

## How It Works Now

### Orchestrator Flow

1. **Receives user goal**: "How is the prompt registry working?"

2. **Outputs routing decision**:
   ```
   ## Routing Decision
   
   **Next Agent**: Repository Explorer
   
   **Reasoning**: Need to understand project structure and locate prompt registry code.
   
   **Focus Areas**: Prompt registry location, configuration, architecture
   ```

3. **Router function parses response**:
   - Looks for "Repository Explorer" in content
   - Routes to `repository_explorer_agent` node

4. **Repository Explorer executes**:
   - Uses its 6 tools to analyze project structure
   - Finds prompt registry files
   - Returns findings via handover

5. **Orchestrator receives findings**:
   - Analyzes what Repository Explorer found
   - Decides next specialist

6. **Outputs next routing decision**:
   ```
   ## Routing Decision
   
   **Next Agent**: Code Navigator
   
   **Reasoning**: Repository Explorer found the registry; now need to analyze implementation.
   
   **Focus Areas**: Registry class, prompt loading, template rendering
   ```

7. **Process continues** until Context Synthesizer produces final report

### Router Function Logic

From `orchestrator.py` lines 244-264:

```python
def _router(self, tool_registry, state):
    content = last_message.content.lower()
    
    if "repository explorer" in content:
        return OrchestratorRoutes.REPOSITORY_EXPLORER
    elif "code navigator" in content:
        return OrchestratorRoutes.CODE_NAVIGATOR
    elif "gitlab ecosystem" in content:
        return OrchestratorRoutes.GITLAB_ECOSYSTEM
    elif "git history" in content:
        return OrchestratorRoutes.GIT_HISTORY
    elif "context synthesizer" in content:
        return OrchestratorRoutes.CONTEXT_SYNTHESIZER
    else:
        return OrchestratorRoutes.STOP
```

The router looks for agent names in the orchestrator's response content.

## Expected Behavior After Fix

### User Query: "How is the prompt registry working?"

**Orchestrator Turn 1** (2-3 seconds):
```
## Routing Decision
**Next Agent**: Repository Explorer
**Reasoning**: Need to locate prompt registry and understand project structure.
**Focus Areas**: Prompt registry location, configuration files, architecture
```

**Repository Explorer** (10-15 seconds):
- Executes tools: list_dir, find_files, read_file, grep
- Finds: `ai_gateway/prompts/`, `prompts/registry.py`, `prompts/definitions/`
- Returns findings via handover

**Orchestrator Turn 2** (2-3 seconds):
```
## Routing Decision
**Next Agent**: Code Navigator
**Reasoning**: Repository Explorer found the registry; need to analyze implementation.
**Focus Areas**: Registry class, prompt loading, template rendering, caching
```

**Code Navigator** (15-20 seconds):
- Executes tools: read_files, grep, gitlab_blob_search
- Analyzes: PromptRegistry class, get_on_behalf method, template system
- Returns detailed code analysis

**Orchestrator Turn 3** (2-3 seconds):
```
## Routing Decision
**Next Agent**: Context Synthesizer
**Reasoning**: Have comprehensive understanding of structure and implementation.
**Focus Areas**: Integrate findings, explain workflow, identify key components
```

**Context Synthesizer** (10-15 seconds):
- Aggregates all findings
- Validates quality metrics
- Produces final comprehensive report
- Hands over to planning phase

**Total Time**: ~45-60 seconds with multiple specialist agents working

## Testing

To verify the fix:

1. **Restart GDK** to load new prompt:
   ```bash
   gdk restart
   ```

2. **Trigger workflow** with a query like:
   - "How does the prompt registry work?"
   - "Explain the authentication flow"
   - "Find the bug in the payment service"

3. **Check LangSmith traces**:
   - Should see multiple agent invocations
   - Orchestrator responses should be short (< 200 tokens)
   - Each specialist should have its own trace

4. **Verify routing**:
   - Orchestrator → Repository Explorer → Orchestrator → Code Navigator → Orchestrator → Context Synthesizer

## Status

✅ **FIXED** - Orchestrator now outputs routing decisions instead of performing analysis
✅ **TESTED** - Prompt format enforces routing-only behavior
✅ **DOCUMENTED** - Clear examples and guidelines in prompt

## Next Steps

1. Test with various query types (bug investigation, feature planning, etc.)
2. Monitor LangSmith traces to ensure proper routing
3. Adjust routing logic if orchestrator output format varies
4. Consider adding structured output (JSON) for more reliable parsing

